#!/usr/bin/env python3
"""
Simplified Keno Predictor - Đơn gi<PERSON>n hóa hệ thống dự đoán
Chỉ bao gồm:
1. LSTM Model - dự đoán 5 số
2. <PERSON><PERSON><PERSON> hình loại bỏ số nóng
3. Top 10 số lạnh trong ngày
=> Chọn 5 số cuối cùng
"""

import numpy as np
import time
from datetime import datetime
from variable_length_model import VariableLengthKenoModel, connect_db

class SimplifiedKenoPredictor:
    """
    Simplified Predictor với 3 thành phần chính:
    1. LSTM Model predictions (5 số)
    2. Exclusion configuration (loại bỏ số nóng)
    3. Cold numbers detection (top 10 số lạnh)
    """

    def __init__(self, exclusion_threshold=3, exclusion_window=5, cold_number_count=10):
        self.lstm_model = None
        self.model_path = "keno_30_period_model.h5"

        # C<PERSON>u hình loại bỏ số
        self.exclusion_threshold = exclusion_threshold  # <PERSON><PERSON><PERSON> bỏ nếu xu<PERSON>t hiện >X lần
        self.exclusion_window = exclusion_window        # Trong Y kì gần nhất

        # C<PERSON>u hình số lạnh
        self.cold_number_count = cold_number_count      # Lấy top X số lạnh

        self.load_lstm_model()

    def load_lstm_model(self):
        """Load LSTM model"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
            print("✅ LSTM model loaded successfully")
        except Exception as e:
            print(f"❌ Error loading LSTM model: {e}")
            self.lstm_model = None

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < 30:
                return None

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            return data

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None

    def get_model_predictions(self, day_results=None, num_predictions=10):
        """Lấy 10 số trượt cao nhất từ LSTM model"""
        if not self.lstm_model:
            print("❌ LSTM model not available")
            return []

        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data or len(data) < 50:
                print("❌ Not enough data for LSTM prediction")
                return []
            day_results = [draw['numbers'] for draw in data[-50:]]

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for LSTM prediction")
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]
            print(f"🤖 LSTM top {num_predictions} predictions: {predictions}")
            return predictions

        except Exception as e:
            print(f"❌ LSTM prediction error: {e}")
            return []

    def get_excluded_numbers(self, day_results):
        """Lấy danh sách số bị loại bỏ (số nóng)"""
        if not day_results or len(day_results) < self.exclusion_window:
            return set()

        # Lấy exclusion_window kì gần nhất
        recent_draws = day_results[-self.exclusion_window:]
        excluded_numbers = set()

        # Đếm số lần xuất hiện của mỗi số
        for num in range(1, 81):
            appearance_count = 0
            for draw in recent_draws:
                if num in draw:
                    appearance_count += 1

            # Nếu xuất hiện quá exclusion_threshold lần, loại bỏ
            if appearance_count > self.exclusion_threshold:
                excluded_numbers.add(num)

        if excluded_numbers:
            print(f"🚫 Excluded numbers (hot): {sorted(list(excluded_numbers))}")
        else:
            print("✅ No numbers excluded")

        return excluded_numbers

    def get_hot_cold_numbers(self, day_results=None):
        """Lấy 10 hot numbers và 10 cold numbers từ 50 kì gần nhất"""
        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data or len(data) < 50:
                print("❌ Not enough data for hot/cold analysis")
                return [], []
            day_results = [draw['numbers'] for draw in data[-50:]]

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for hot/cold analysis")
            return [], []

        # Lấy 50 kì gần nhất
        recent_50_periods = day_results[-50:]

        # Đếm số lần xuất hiện của mỗi số trong 50 kì gần nhất
        number_counts = {}
        for num in range(1, 81):
            count = 0
            for draw in recent_50_periods:
                if num in draw:
                    count += 1
            number_counts[num] = count

        # Sắp xếp theo số lần xuất hiện
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1])

        # Top 10 cold numbers (ít xuất hiện nhất)
        cold_numbers = [num for num, count in sorted_numbers[:10]]

        # Top 10 hot numbers (xuất hiện nhiều nhất)
        hot_numbers = [num for num, count in sorted_numbers[-10:]]
        hot_numbers.reverse()  # Sắp xếp từ nóng nhất đến ít nóng nhất

        print(f"🔥 Top 10 hot numbers (50 kì): {hot_numbers}")
        print(f"🧊 Top 10 cold numbers (50 kì): {cold_numbers}")

        return hot_numbers, cold_numbers

    def analyze_matches_and_prioritize(self, lstm_predictions, hot_numbers, cold_numbers):
        """
        CHIẾN LƯỢC MỚI: Tránh 3 top số nóng đầu tiên và top 2 số lạnh
        Logic: Số quá nóng hoặc quá lạnh thường có xu hướng nghỉ ngơi
        """

        # Định nghĩa các nhóm số cần tránh
        avoid_hot = hot_numbers[:3]      # Top 3 số nóng nhất - TRÁNH
        avoid_cold = cold_numbers[:2]    # Top 2 số lạnh nhất - TRÁNH

        # Định nghĩa các nhóm số ưu tiên
        good_hot = hot_numbers[3:7]      # Số 4-7 hot - ưu tiên cao (vẫn nóng nhưng không quá)
        good_cold = cold_numbers[2:6]    # Số 3-6 cold - ưu tiên trung bình (bắt đầu lạnh)
        neutral_hot = hot_numbers[7:10]  # Số 8-10 hot - ưu tiên trung bình
        neutral_cold = cold_numbers[6:10] # Số 7-10 cold - ưu tiên thấp

        print(f"🎯 CHIẾN LƯỢC TRÁNH SỐ:")
        print(f"   🚫 TRÁNH - Top 3 hot: {avoid_hot}")
        print(f"   🚫 TRÁNH - Top 2 cold: {avoid_cold}")
        print(f"   ⭐ ƯU TIÊN - Good hot (4-7): {good_hot}")
        print(f"   🔸 TRUNG BÌNH - Good cold (3-6): {good_cold}")
        print(f"   🔹 TRUNG BÌNH - Neutral hot (8-10): {neutral_hot}")
        print(f"   ➖ THẤP - Neutral cold (7-10): {neutral_cold}")

        # Phân loại LSTM predictions
        high_priority = []    # Ưu tiên cao nhất
        medium_priority = []  # Ưu tiên trung bình
        low_priority = []     # Ưu tiên thấp
        avoid_numbers = []    # Số cần tránh

        for num in lstm_predictions:
            if num in avoid_hot:
                # Top 3 hot => TRÁNH HOÀN TOÀN
                avoid_numbers.append(num)
                print(f"   🚫 {num} in top 3 hot - AVOID COMPLETELY")
            elif num in avoid_cold:
                # Top 2 cold => TRÁNH HOÀN TOÀN
                avoid_numbers.append(num)
                print(f"   🚫 {num} in top 2 cold - AVOID COMPLETELY")
            elif num in good_hot:
                # Số 4-7 hot => ưu tiên cao nhất
                high_priority.append(num)
                print(f"   ⭐ {num} in good hot range (4-7) - HIGH priority")
            elif num in good_cold:
                # Số 3-6 cold => ưu tiên trung bình
                medium_priority.append(num)
                print(f"   🔸 {num} in good cold range (3-6) - MEDIUM priority")
            elif num in neutral_hot:
                # Số 8-10 hot => ưu tiên trung bình
                medium_priority.append(num)
                print(f"   🔹 {num} in neutral hot range (8-10) - MEDIUM priority")
            elif num in neutral_cold:
                # Số 7-10 cold => ưu tiên thấp
                low_priority.append(num)
                print(f"   ➖ {num} in neutral cold range (7-10) - LOW priority")
            else:
                # Không thuộc hot/cold top 10 => ưu tiên cao nhất (số cân bằng)
                high_priority.append(num)
                print(f"   ✅ {num} balanced number - HIGH priority")

        # Kết hợp theo thứ tự ưu tiên (BỎ QUA avoid_numbers)
        prioritized_numbers = high_priority + medium_priority + low_priority

        print(f"🎯 Final prioritized order: {prioritized_numbers}")
        print(f"🚫 Avoided numbers: {avoid_numbers}")

        return prioritized_numbers, avoid_numbers

    def select_final_5_numbers(self, lstm_predictions, hot_numbers, cold_numbers, excluded_numbers):
        """Chọn 5 số cuối cùng với chiến lược tránh số mới"""

        print(f"\n🔄 FINAL SELECTION PROCESS:")
        print(f"   LSTM predictions: {lstm_predictions}")
        print(f"   Excluded numbers: {sorted(list(excluded_numbers))}")

        # Bước 1: Phân tích matches và ưu tiên (trả về cả avoid_numbers)
        prioritized_lstm, avoid_numbers = self.analyze_matches_and_prioritize(lstm_predictions, hot_numbers, cold_numbers)

        # Bước 2: Kết hợp excluded_numbers với avoid_numbers
        all_excluded = excluded_numbers.union(set(avoid_numbers))
        print(f"   All excluded (original + avoided): {sorted(list(all_excluded))}")

        # Bước 3: Loại bỏ tất cả số bị excluded và avoided
        valid_candidates = []
        for num in prioritized_lstm:
            if num not in all_excluded:
                valid_candidates.append(num)

        print(f"   Valid candidates (after all exclusions): {valid_candidates}")

        # Bước 4: Chọn 5 số đầu tiên
        final_numbers = valid_candidates[:5]

        # Bước 5: Nếu không đủ 5 số, bổ sung từ good hot numbers (4-7)
        if len(final_numbers) < 5:
            print(f"   Need {5 - len(final_numbers)} more numbers, adding from good hot (4-7)...")
            good_hot = hot_numbers[3:7]  # Số 4-7 hot
            for num in good_hot:
                if num not in all_excluded and num not in final_numbers and len(final_numbers) < 5:
                    final_numbers.append(num)
                    print(f"   + Added good hot number: {num}")

        # Bước 6: Nếu vẫn không đủ, bổ sung từ neutral numbers
        if len(final_numbers) < 5:
            print(f"   Still need {5 - len(final_numbers)} more numbers, adding neutral...")
            all_numbers = set(range(1, 81))
            hot_cold_set = set(hot_numbers + cold_numbers)
            neutral_numbers = list(all_numbers - hot_cold_set - all_excluded - set(final_numbers))

            needed = min(5 - len(final_numbers), len(neutral_numbers))
            if needed > 0:
                import random
                additional_numbers = random.sample(neutral_numbers, needed)
                final_numbers.extend(additional_numbers)
                print(f"   + Added neutral numbers: {additional_numbers}")

        # Bước 7: Cuối cùng nếu vẫn không đủ, bổ sung bất kỳ số nào còn lại
        if len(final_numbers) < 5:
            print(f"   Emergency: need {5 - len(final_numbers)} more numbers...")
            all_numbers = set(range(1, 81))
            available_numbers = all_numbers - set(final_numbers)

            needed = min(5 - len(final_numbers), len(available_numbers))
            if needed > 0:
                import random
                emergency_numbers = random.sample(list(available_numbers), needed)
                final_numbers.extend(emergency_numbers)
                print(f"   + Emergency numbers: {emergency_numbers}")

        return final_numbers[:5]

    def predict(self, day_results=None):
        """Main prediction function - trả về 5 số cuối cùng với logic mới"""
        print("\n" + "="*50)
        print("🎯 SIMPLIFIED KENO PREDICTION (UPDATED)")
        print("="*50)

        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data:
                print("❌ Cannot load data")
                return []

            # Sử dụng tất cả dữ liệu có sẵn (không chỉ ngày hiện tại)
            day_results = [draw['numbers'] for draw in data]
            print(f"📅 Using {len(day_results)} periods from recent data")

        # 1. Lấy 10 số trượt cao nhất từ LSTM model
        lstm_predictions = self.get_model_predictions(day_results, num_predictions=10)

        # 2. Lấy 10 hot numbers và 10 cold numbers từ 50 kì gần nhất
        hot_numbers, cold_numbers = self.get_hot_cold_numbers(day_results)

        # 3. Lấy danh sách số bị loại bỏ tạm thời
        excluded_numbers = self.get_excluded_numbers(day_results)

        # 4. Chọn 5 số cuối cùng với logic ưu tiên mới
        final_predictions = self.select_final_5_numbers(
            lstm_predictions, hot_numbers, cold_numbers, excluded_numbers
        )

        print(f"\n🎯 FINAL PREDICTION: {final_predictions}")
        print("="*50)

        return final_predictions

    def get_test_dates(self):
        """Lấy danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            return [row['date'] for row in rows]

        except Exception as e:
            print(f"❌ Error getting test dates: {e}")
            return []

    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            print(f"❌ Error getting day draws: {e}")
            return []

    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0, 0, 0, [], []

        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)

        # Số dự đoán đúng và sai
        correct_numbers = list(predicted_set & actual_missing)  # Số dự đoán đúng
        wrong_numbers = list(predicted_set - actual_missing)    # Số dự đoán sai

        # Số dự đoán đúng
        correct_predictions = len(correct_numbers)

        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100

        return accuracy, correct_predictions, len(predicted_missing), correct_numbers, wrong_numbers

    def analyze_hot_cold_distribution(self, numbers, hot_numbers, cold_numbers, label="Numbers"):
        """Phân tích phân bố hot/cold của một danh sách số"""
        if not numbers:
            return {}

        # Chia hot/cold thành các nhóm
        top_3_hot = hot_numbers[:3]
        mid_hot = hot_numbers[3:7]  # 4-7
        low_hot = hot_numbers[7:10]  # 8-10

        top_2_cold = cold_numbers[:2]
        mid_cold = cold_numbers[2:6]  # 3-6
        low_cold = cold_numbers[6:10]  # 7-10

        # Phân loại từng số
        analysis = {
            'top_3_hot': [],
            'mid_hot': [],
            'low_hot': [],
            'top_2_cold': [],
            'mid_cold': [],
            'low_cold': [],
            'neutral': []
        }

        for num in numbers:
            if num in top_3_hot:
                analysis['top_3_hot'].append(num)
            elif num in mid_hot:
                analysis['mid_hot'].append(num)
            elif num in low_hot:
                analysis['low_hot'].append(num)
            elif num in top_2_cold:
                analysis['top_2_cold'].append(num)
            elif num in mid_cold:
                analysis['mid_cold'].append(num)
            elif num in low_cold:
                analysis['low_cold'].append(num)
            else:
                analysis['neutral'].append(num)

        return analysis

    def log_detailed_analysis(self, predicted_missing, actual_results, hot_numbers, cold_numbers, draw_number, actual_time):
        """Log phân tích chi tiết về hot/cold distribution"""

        # Số trượt thực tế
        actual_missing = list(set(range(1, 81)) - set(actual_results))

        # Phân tích số dự đoán
        pred_analysis = self.analyze_hot_cold_distribution(predicted_missing, hot_numbers, cold_numbers, "Predicted")

        # Phân tích số trượt thực tế
        actual_analysis = self.analyze_hot_cold_distribution(actual_missing, hot_numbers, cold_numbers, "Actual Missing")

        # Phân tích số ra thực tế
        actual_analysis_drawn = self.analyze_hot_cold_distribution(actual_results, hot_numbers, cold_numbers, "Actual Drawn")

        print(f"  📊 DETAILED ANALYSIS for period {draw_number} ({actual_time}):")
        print(f"     🎯 Predicted missing: {predicted_missing}")
        print(f"     ❌ Actual missing: {len(actual_missing)} numbers")
        print(f"     ✅ Actual drawn: {actual_results}")

        print(f"  📈 PREDICTED DISTRIBUTION:")
        if pred_analysis['top_3_hot']:
            print(f"     🚫 Top 3 hot: {pred_analysis['top_3_hot']} (AVOIDED)")
        if pred_analysis['mid_hot']:
            print(f"     ⭐ Mid hot (4-7): {pred_analysis['mid_hot']} (PREFERRED)")
        if pred_analysis['low_hot']:
            print(f"     🔹 Low hot (8-10): {pred_analysis['low_hot']}")
        if pred_analysis['top_2_cold']:
            print(f"     🚫 Top 2 cold: {pred_analysis['top_2_cold']} (AVOIDED)")
        if pred_analysis['mid_cold']:
            print(f"     🔸 Mid cold (3-6): {pred_analysis['mid_cold']}")
        if pred_analysis['low_cold']:
            print(f"     ➖ Low cold (7-10): {pred_analysis['low_cold']}")
        if pred_analysis['neutral']:
            print(f"     ⚪ Neutral: {pred_analysis['neutral']}")

        print(f"  🎲 ACTUAL DRAWN DISTRIBUTION:")
        if actual_analysis_drawn['top_3_hot']:
            print(f"     🔥 Top 3 hot drawn: {actual_analysis_drawn['top_3_hot']}")
        if actual_analysis_drawn['mid_hot']:
            print(f"     🔥 Mid hot drawn: {actual_analysis_drawn['mid_hot']}")
        if actual_analysis_drawn['low_hot']:
            print(f"     🔥 Low hot drawn: {actual_analysis_drawn['low_hot']}")
        if actual_analysis_drawn['top_2_cold']:
            print(f"     🧊 Top 2 cold drawn: {actual_analysis_drawn['top_2_cold']}")
        if actual_analysis_drawn['mid_cold']:
            print(f"     🧊 Mid cold drawn: {actual_analysis_drawn['mid_cold']}")
        if actual_analysis_drawn['low_cold']:
            print(f"     🧊 Low cold drawn: {actual_analysis_drawn['low_cold']}")
        if actual_analysis_drawn['neutral']:
            print(f"     ⚪ Neutral drawn: {actual_analysis_drawn['neutral']}")

        # Tính hiệu quả chiến lược
        strategy_effectiveness = {
            'avoided_top3_hot_correctly': len([n for n in hot_numbers[:3] if n not in actual_results]),
            'avoided_top2_cold_correctly': len([n for n in cold_numbers[:2] if n not in actual_results]),
            'preferred_mid_hot_success': len([n for n in pred_analysis['mid_hot'] if n not in actual_results]),
            'total_avoided': len(hot_numbers[:3]) + len(cold_numbers[:2]),
            'total_preferred': len(pred_analysis['mid_hot'])
        }

        print(f"  🎯 STRATEGY EFFECTIVENESS:")
        print(f"     Avoided top 3 hot correctly: {strategy_effectiveness['avoided_top3_hot_correctly']}/3")
        print(f"     Avoided top 2 cold correctly: {strategy_effectiveness['avoided_top2_cold_correctly']}/2")
        if strategy_effectiveness['total_preferred'] > 0:
            print(f"     Preferred mid hot success: {strategy_effectiveness['preferred_mid_hot_success']}/{strategy_effectiveness['total_preferred']}")

        return strategy_effectiveness

    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày từ kì 51"""
        print(f"\n=== Test ngày {test_date} ===")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        predictions_count = 0
        perfect_predictions = 0  # Số lần dự đoán đúng ≥4/5 số trượt
        good_predictions = 0     # Số lần dự đoán đúng 5/5 số trượt
        normal_predictions = 0   # Số lần dự đoán đúng 4/5 số trượt
        bad_predictions = 0      # Số lần dự đoán đúng ≤3/5 số trượt

        # Test từ kì 51 đến cuối ngày
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 5 số trượt với logic mới (tắt logging chi tiết trong test)
            try:
                # Tạm thời tắt print để test nhanh hơn
                import sys
                from io import StringIO
                old_stdout = sys.stdout
                sys.stdout = StringIO()

                predicted_missing = self.predict(day_results=input_draws)

                # Khôi phục stdout
                sys.stdout = old_stdout

                if predicted_missing:
                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']
                    actual_time = day_draws[draw_index]['time']

                    # Tính độ chính xác
                    _, correct, _, _, _ = self.calculate_accuracy(predicted_missing, actual_results)

                    predictions_count += 1

                    # Đếm các loại dự đoán
                    if correct >= 5:
                        good_predictions += 1  # 5/5 = Good
                        perfect_predictions += 1
                    elif correct >= 4:
                        normal_predictions += 1  # 4/5 = Normal
                        perfect_predictions += 1
                    else:
                        bad_predictions += 1  # ≤3/5 = Bad

                    # Hiển thị kết quả với icon khác nhau
                    if correct >= 5:
                        label = "Good"
                        status_icon = "⭐"  # 5/5 đúng
                    elif correct >= 4:
                        label = "Normal"
                        status_icon = "🔥"  # 4/5 đúng - icon khác
                    elif correct <= 2:
                        label = "Bad"
                        status_icon = "❌"  # ≤2/5 đúng
                    else:
                        label = "Bad"
                        status_icon = "⚠️"  # 3/5 đúng

                    print(f"Kì {draw_number:3d} ({actual_time}): {correct}/5 đúng - {label} {status_icon}")

                    # Log chi tiết hot/cold distribution (chỉ cho kết quả tốt)
                    if correct >= 4:  # Chỉ log chi tiết cho Good và Normal
                        # Lấy hot/cold numbers từ 50 kì gần nhất
                        recent_50 = input_draws[-50:] if len(input_draws) >= 50 else input_draws
                        hot_numbers, cold_numbers = self.get_hot_cold_numbers(recent_50)

                        # Khôi phục stdout để log chi tiết
                        sys.stdout = old_stdout
                        self.log_detailed_analysis(predicted_missing, actual_results, hot_numbers, cold_numbers, draw_number, actual_time)
                        # Tắt lại stdout
                        sys.stdout = StringIO()

            except Exception as e:
                print(f"❌ Error predicting period {draw_number}: {e}")

        # Tính độ chính xác cho cả ngày
        if predictions_count > 0:
            perfect_rate = (perfect_predictions / predictions_count) * 100
            good_rate = (good_predictions / predictions_count) * 100
            normal_rate = (normal_predictions / predictions_count) * 100
            bad_rate = (bad_predictions / predictions_count) * 100

            print(f"📊 {test_date}: Perfect(≥4/5): {perfect_predictions}/{predictions_count} = {perfect_rate:.1f}%")

            result = {
                'date': str(test_date),
                'predictions_count': predictions_count,
                'perfect_predictions': perfect_predictions,
                'perfect_rate': perfect_rate,
                'good_predictions': good_predictions,
                'good_rate': good_rate,
                'normal_predictions': normal_predictions,
                'normal_rate': normal_rate,
                'bad_predictions': bad_predictions,
                'bad_rate': bad_rate
            }

            return result

        return None

    def test_single_day_with_detailed_logging(self, test_date):
        """Test dự đoán cho một ngày với detailed logging (không tắt stdout)"""
        print(f"\n=== Test chi tiết ngày {test_date} ===")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        predictions_count = 0
        perfect_predictions = 0  # Số lần dự đoán đúng ≥4/5 số trượt
        good_predictions = 0     # Số lần dự đoán đúng 5/5 số trượt
        normal_predictions = 0   # Số lần dự đoán đúng 4/5 số trượt
        bad_predictions = 0      # Số lần dự đoán đúng ≤3/5 số trượt

        # Thống kê chiến lược tổng hợp
        total_strategy_stats = {
            'avoided_top3_hot_correctly': 0,
            'avoided_top2_cold_correctly': 0,
            'preferred_mid_hot_success': 0,
            'total_avoided_attempts': 0,
            'total_preferred_attempts': 0
        }

        # Test từ kì 51 đến cuối ngày (hoặc chỉ test 10 kì đầu để demo)
        max_test_periods = min(len(day_draws), 60)  # Test tối đa 10 kì để không quá dài

        for draw_index in range(50, max_test_periods):  # Từ kì 51
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 5 số trượt với full logging
            try:
                print(f"\n🔄 PREDICTING PERIOD {draw_number}:")
                predicted_missing = self.predict(day_results=input_draws)

                if predicted_missing:
                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']
                    actual_time = day_draws[draw_index]['time']

                    # Tính độ chính xác
                    _, correct, _, _, _ = self.calculate_accuracy(predicted_missing, actual_results)

                    predictions_count += 1

                    # Đếm các loại dự đoán
                    if correct >= 5:
                        good_predictions += 1  # 5/5 = Good
                        perfect_predictions += 1
                    elif correct >= 4:
                        normal_predictions += 1  # 4/5 = Normal
                        perfect_predictions += 1
                    else:
                        bad_predictions += 1  # ≤3/5 = Bad

                    # Hiển thị kết quả với icon khác nhau
                    if correct >= 5:
                        label = "Good"
                        status_icon = "⭐"  # 5/5 đúng
                    elif correct >= 4:
                        label = "Normal"
                        status_icon = "🔥"  # 4/5 đúng - icon khác
                    elif correct <= 2:
                        label = "Bad"
                        status_icon = "❌"  # ≤2/5 đúng
                    else:
                        label = "Bad"
                        status_icon = "⚠️"  # 3/5 đúng

                    print(f"\n✅ RESULT: Kì {draw_number:3d} ({actual_time}): {correct}/5 đúng - {label} {status_icon}")

                    # Log chi tiết hot/cold distribution cho tất cả kết quả
                    # Lấy hot/cold numbers từ 50 kì gần nhất
                    recent_50 = input_draws[-50:] if len(input_draws) >= 50 else input_draws
                    hot_numbers, cold_numbers = self.get_hot_cold_numbers(recent_50)

                    strategy_effectiveness = self.log_detailed_analysis(
                        predicted_missing, actual_results, hot_numbers, cold_numbers, draw_number, actual_time
                    )

                    # Cập nhật thống kê tổng hợp
                    total_strategy_stats['avoided_top3_hot_correctly'] += strategy_effectiveness['avoided_top3_hot_correctly']
                    total_strategy_stats['avoided_top2_cold_correctly'] += strategy_effectiveness['avoided_top2_cold_correctly']
                    total_strategy_stats['preferred_mid_hot_success'] += strategy_effectiveness['preferred_mid_hot_success']
                    total_strategy_stats['total_avoided_attempts'] += strategy_effectiveness['total_avoided']
                    total_strategy_stats['total_preferred_attempts'] += strategy_effectiveness['total_preferred']

                    print("-" * 80)

            except Exception as e:
                print(f"❌ Error predicting period {draw_number}: {e}")

        # Tính độ chính xác cho cả ngày
        if predictions_count > 0:
            perfect_rate = (perfect_predictions / predictions_count) * 100
            good_rate = (good_predictions / predictions_count) * 100
            normal_rate = (normal_predictions / predictions_count) * 100
            bad_rate = (bad_predictions / predictions_count) * 100

            print(f"\n📊 DAILY SUMMARY FOR {test_date}:")
            print(f"   Perfect(≥4/5): {perfect_predictions}/{predictions_count} = {perfect_rate:.1f}%")
            print(f"   Good(5/5): {good_predictions}/{predictions_count} = {good_rate:.1f}%")
            print(f"   Normal(4/5): {normal_predictions}/{predictions_count} = {normal_rate:.1f}%")
            print(f"   Bad(≤3/5): {bad_predictions}/{predictions_count} = {bad_rate:.1f}%")

            # Thống kê chiến lược tổng hợp
            print(f"\n🎯 STRATEGY EFFECTIVENESS SUMMARY:")
            if total_strategy_stats['total_avoided_attempts'] > 0:
                avoid_success_rate = ((total_strategy_stats['avoided_top3_hot_correctly'] +
                                     total_strategy_stats['avoided_top2_cold_correctly']) /
                                    total_strategy_stats['total_avoided_attempts']) * 100
                print(f"   Avoid strategy success: {total_strategy_stats['avoided_top3_hot_correctly'] + total_strategy_stats['avoided_top2_cold_correctly']}/{total_strategy_stats['total_avoided_attempts']} = {avoid_success_rate:.1f}%")

            if total_strategy_stats['total_preferred_attempts'] > 0:
                prefer_success_rate = (total_strategy_stats['preferred_mid_hot_success'] /
                                     total_strategy_stats['total_preferred_attempts']) * 100
                print(f"   Prefer strategy success: {total_strategy_stats['preferred_mid_hot_success']}/{total_strategy_stats['total_preferred_attempts']} = {prefer_success_rate:.1f}%")

            result = {
                'date': str(test_date),
                'predictions_count': predictions_count,
                'perfect_predictions': perfect_predictions,
                'perfect_rate': perfect_rate,
                'good_predictions': good_predictions,
                'good_rate': good_rate,
                'normal_predictions': normal_predictions,
                'normal_rate': normal_rate,
                'bad_predictions': bad_predictions,
                'bad_rate': bad_rate,
                'strategy_stats': total_strategy_stats
            }

            return result

        return None

    def test_all_days(self):
        """Test tất cả ngày từ 2025-04-01"""
        print("=== Test độ chính xác dự đoán (từ 2025-04-01) ===")
        print("Sử dụng: Simplified Keno Predictor")
        print(f"- LSTM Model + Loại bỏ số nóng (>{self.exclusion_threshold} lần/{self.exclusion_window} kì)")
        print(f"- Top {self.cold_number_count} số lạnh")

        test_dates = self.get_test_dates()

        if not test_dates:
            print("Không có ngày nào để test")
            return

        print(f"Sẽ test {len(test_dates)} ngày")

        total_perfect_predictions = 0
        total_prediction_count = 0
        successful_days = 0
        daily_results = []

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}]", end=" ")

            day_result = self.test_single_day(test_date)

            if day_result:
                total_perfect_predictions += day_result['perfect_predictions']
                total_prediction_count += day_result['predictions_count']
                successful_days += 1
                daily_results.append(day_result)

        # Tổng kết
        if total_prediction_count > 0:
            overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100

            print(f"\n=== TỔNG KẾT ===")
            print(f"Dự đoán xuất sắc (≥4/5): {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.2f}%)")
            print(f"Số ngày test: {successful_days}/{len(test_dates)}")
            print(f"Phương pháp: Simplified Predictor (LSTM + Exclusion + Cold Numbers)")

            # Thống kê chi tiết
            self.print_daily_summary(daily_results, total_perfect_predictions, total_prediction_count)
        else:
            print("\nKhông có kết quả test nào")

    def print_daily_summary(self, daily_results, total_perfect_predictions, total_prediction_count):
        """In thống kê chi tiết các ngày"""
        print(f"\n" + "="*70)
        print("📊 THỐNG KÊ CHI TIẾT CÁC NGÀY")
        print("="*70)

        if not daily_results:
            print("Không có dữ liệu ngày để thống kê")
            return

        # Sắp xếp theo ngày
        sorted_results = sorted(daily_results, key=lambda x: x['date'])

        # Header bảng
        print(f"{'Ngày':<12} {'Good':<8} {'Normal':<8} {'≥4/5':<8} {'Bad':<8} {'Kì':<5}")
        print("-" * 60)

        # Tính tổng
        total_good = sum(r['good_predictions'] for r in sorted_results)
        total_normal = sum(r['normal_predictions'] for r in sorted_results)
        total_bad = sum(r['bad_predictions'] for r in sorted_results)

        # In từng ngày
        for result in sorted_results:
            date_str = str(result['date'])
            good_str = f"{result['good_predictions']}({result['good_rate']:.0f}%)"
            normal_str = f"{result['normal_predictions']}({result['normal_rate']:.0f}%)"
            perfect_str = f"{result['perfect_predictions']}({result['perfect_rate']:.0f}%)"
            bad_str = f"{result['bad_predictions']}({result['bad_rate']:.0f}%)"
            predictions_str = f"{result['predictions_count']}"

            print(f"{date_str:<12} {good_str:<8} {normal_str:<8} {perfect_str:<8} {bad_str:<8} {predictions_str:<5}")

        # Thống kê tổng hợp
        print("-" * 60)
        overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100
        overall_good_rate = (total_good / total_prediction_count) * 100
        overall_normal_rate = (total_normal / total_prediction_count) * 100
        overall_bad_rate = (total_bad / total_prediction_count) * 100

        good_total_str = f"{total_good}({overall_good_rate:.0f}%)"
        normal_total_str = f"{total_normal}({overall_normal_rate:.0f}%)"
        perfect_total_str = f"{total_perfect_predictions}({overall_perfect_rate:.0f}%)"
        bad_total_str = f"{total_bad}({overall_bad_rate:.0f}%)"

        print(f"{'TỔNG':<12} {good_total_str:<8} {normal_total_str:<8} {perfect_total_str:<8} {bad_total_str:<8} {total_prediction_count:<5}")

        print(f"\n📈 PHÂN TÍCH CHI TIẾT:")
        print(f"   • 5/5 đúng (Good):      {total_good}/{total_prediction_count} lần ({overall_good_rate:.1f}%)")
        print(f"   • 4/5 đúng (Normal):    {total_normal}/{total_prediction_count} lần ({overall_normal_rate:.1f}%)")
        print(f"   • ≥4/5 đúng (Perfect):  {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.1f}%)")
        print(f"   • ≤3/5 đúng (Bad):      {total_bad}/{total_prediction_count} lần ({overall_bad_rate:.1f}%)")

        print("="*70)

def predict_5_numbers():
    """Dự đoán nhanh 5 số"""
    predictor = SimplifiedKenoPredictor()
    return predictor.predict()

def test_strategy_logic():
    """Test logic chiến lược mà không cần LSTM model"""
    print("🧪 TESTING STRATEGY LOGIC")
    print("="*50)

    # Tạo dữ liệu giả để test logic
    fake_lstm_predictions = [1, 5, 12, 23, 34, 45, 56, 67, 78, 80]
    fake_hot_numbers = [23, 45, 67, 12, 34, 56, 78, 1, 5, 80]  # Top 10 hot
    fake_cold_numbers = [15, 25, 35, 40, 50, 60, 70, 75, 77, 79]  # Top 10 cold
    fake_excluded = {23}  # Số 23 bị loại trừ

    print(f"📊 Test data:")
    print(f"   LSTM predictions: {fake_lstm_predictions}")
    print(f"   Hot numbers: {fake_hot_numbers}")
    print(f"   Cold numbers: {fake_cold_numbers}")
    print(f"   Excluded: {fake_excluded}")

    # Tạo predictor và test logic
    predictor = SimplifiedKenoPredictor()

    # Test analyze_matches_and_prioritize
    _, _ = predictor.analyze_matches_and_prioritize(
        fake_lstm_predictions, fake_hot_numbers, fake_cold_numbers
    )

    # Test select_final_5_numbers
    final_5 = predictor.select_final_5_numbers(
        fake_lstm_predictions, fake_hot_numbers, fake_cold_numbers, fake_excluded
    )

    print(f"\n🎯 FINAL RESULT: {final_5}")
    print("="*50)

    return final_5

def test_detailed_logging():
    """Test detailed logging với dữ liệu giả"""
    print("🧪 TESTING DETAILED LOGGING")
    print("="*50)

    # Tạo dữ liệu giả
    predicted_missing = [12, 34, 56, 78, 1]  # Dự đoán
    actual_results = [23, 45, 67, 89, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17]  # Số ra thực tế
    hot_numbers = [23, 45, 67, 12, 34, 56, 78, 1, 5, 80]  # Top 10 hot
    cold_numbers = [15, 25, 35, 40, 50, 60, 70, 75, 77, 79]  # Top 10 cold

    # Tạo predictor và test logging
    predictor = SimplifiedKenoPredictor()

    # Test detailed analysis
    strategy_effectiveness = predictor.log_detailed_analysis(
        predicted_missing, actual_results, hot_numbers, cold_numbers, 51, "10:00:00"
    )

    print(f"\n📊 Strategy effectiveness: {strategy_effectiveness}")
    print("="*50)

    return strategy_effectiveness

def test_from_2025_04_01():
    """Test với dữ liệu từ 2025-04-01 với detailed logging"""
    predictor = SimplifiedKenoPredictor()
    predictor.test_all_days()

def test_single_day_detailed(test_date="2025-04-01"):
    """Test chi tiết một ngày cụ thể với full logging"""
    print(f"🧪 TESTING SINGLE DAY: {test_date}")
    print("="*60)

    predictor = SimplifiedKenoPredictor()

    # Test một ngày với logging chi tiết
    result = predictor.test_single_day_with_detailed_logging(test_date)

    if result:
        print(f"\n📊 SUMMARY FOR {test_date}:")
        print(f"   Perfect predictions (≥4/5): {result['perfect_predictions']}/{result['predictions_count']} = {result['perfect_rate']:.1f}%")
        print(f"   Good predictions (5/5): {result['good_predictions']}/{result['predictions_count']} = {result['good_rate']:.1f}%")
        print(f"   Normal predictions (4/5): {result['normal_predictions']}/{result['predictions_count']} = {result['normal_rate']:.1f}%")

    return result

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'predict':
            predictions = predict_5_numbers()
            if predictions:
                print(f"\n✅ 5 số dự đoán: {predictions}")
            else:
                print("\n❌ Không thể dự đoán")
        elif mode == 'test':
            test_from_2025_04_01()
        elif mode == 'testlogic':
            test_strategy_logic()
        elif mode == 'testlog':
            test_detailed_logging()
        else:
            print("Sử dụng:")
            print("  python simplified_keno_predictor.py predict      # Dự đoán 5 số")
            print("  python simplified_keno_predictor.py test         # Test từ 2025-04-01")
            print("  python simplified_keno_predictor.py testlogic    # Test logic chiến lược")
            print("  python simplified_keno_predictor.py testlog      # Test detailed logging")
    else:
        # Mặc định: test detailed logging
        test_detailed_logging()

if __name__ == "__main__":
    main()
