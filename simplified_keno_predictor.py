#!/usr/bin/env python3
"""
Simplified Keno Predictor - Đơn gi<PERSON>n hóa hệ thống dự đoán
Chỉ bao gồm:
1. LSTM Model - dự đoán 5 số
2. <PERSON><PERSON><PERSON> hình loại bỏ số nóng
3. Top 10 số lạnh trong ngày
=> Chọn 5 số cuối cùng
"""

import numpy as np
import time
from datetime import datetime
from variable_length_model import VariableLengthKenoModel, connect_db

class SimplifiedKenoPredictor:
    """
    Simplified Predictor với 3 thành phần chính:
    1. LSTM Model predictions (5 số)
    2. Exclusion configuration (loại bỏ số nóng)
    3. Cold numbers detection (top 10 số lạnh)
    """

    def __init__(self, exclusion_threshold=3, exclusion_window=5, cold_number_count=10):
        self.lstm_model = None
        self.model_path = "keno_30_period_model.h5"
        
        # C<PERSON>u hình loại bỏ số
        self.exclusion_threshold = exclusion_threshold  # <PERSON><PERSON><PERSON> bỏ nếu xu<PERSON>t hiện >X lần
        self.exclusion_window = exclusion_window        # Trong Y kì gần nhất
        
        # C<PERSON>u hình số lạnh
        self.cold_number_count = cold_number_count      # Lấy top X số lạnh
        
        self.load_lstm_model()

    def load_lstm_model(self):
        """Load LSTM model"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
            print("✅ LSTM model loaded successfully")
        except Exception as e:
            print(f"❌ Error loading LSTM model: {e}")
            self.lstm_model = None

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < 30:
                return None

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            return data

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None

    def get_model_predictions(self, day_results=None, num_predictions=5):
        """Lấy 5 số dự đoán từ LSTM model"""
        if not self.lstm_model:
            print("❌ LSTM model not available")
            return []

        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data or len(data) < 50:
                print("❌ Not enough data for LSTM prediction")
                return []
            day_results = [draw['numbers'] for draw in data[-50:]]

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for LSTM prediction")
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]
            print(f"🤖 LSTM predictions: {predictions}")
            return predictions

        except Exception as e:
            print(f"❌ LSTM prediction error: {e}")
            return []

    def get_excluded_numbers(self, day_results):
        """Lấy danh sách số bị loại bỏ (số nóng)"""
        if not day_results or len(day_results) < self.exclusion_window:
            return set()

        # Lấy exclusion_window kì gần nhất
        recent_draws = day_results[-self.exclusion_window:]
        excluded_numbers = set()

        # Đếm số lần xuất hiện của mỗi số
        for num in range(1, 81):
            appearance_count = 0
            for draw in recent_draws:
                if num in draw:
                    appearance_count += 1

            # Nếu xuất hiện quá exclusion_threshold lần, loại bỏ
            if appearance_count > self.exclusion_threshold:
                excluded_numbers.add(num)

        if excluded_numbers:
            print(f"🚫 Excluded numbers (hot): {sorted(list(excluded_numbers))}")
        else:
            print("✅ No numbers excluded")
            
        return excluded_numbers

    def get_cold_numbers(self, day_results):
        """Lấy top 10 số lạnh (ít xuất hiện nhất trong ngày hôm đó)"""
        if not day_results:
            return []

        # Đếm số lần xuất hiện của mỗi số trong ngày
        number_counts = {}
        for num in range(1, 81):
            count = 0
            for draw in day_results:
                if num in draw:
                    count += 1
            number_counts[num] = count

        # Sắp xếp theo số lần xuất hiện tăng dần (số lạnh nhất trước)
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1])
        
        # Lấy top cold_number_count số lạnh nhất
        cold_numbers = [num for num, count in sorted_numbers[:self.cold_number_count]]
        
        print(f"🧊 Top {self.cold_number_count} cold numbers: {cold_numbers}")
        return cold_numbers

    def select_final_5_numbers(self, model_predictions, cold_numbers, excluded_numbers):
        """Chọn 5 số cuối cùng từ model + cold numbers, loại trừ excluded numbers"""
        
        # Tạo pool ứng viên từ model predictions + cold numbers
        candidate_pool = set()
        
        # Thêm model predictions (ưu tiên cao)
        for num in model_predictions:
            if num not in excluded_numbers:
                candidate_pool.add(num)
        
        # Thêm cold numbers (ưu tiên thấp hơn)
        for num in cold_numbers:
            if num not in excluded_numbers:
                candidate_pool.add(num)
        
        # Nếu không đủ 5 số, thêm số ngẫu nhiên không bị loại trừ
        if len(candidate_pool) < 5:
            all_numbers = set(range(1, 81))
            available_numbers = all_numbers - excluded_numbers - candidate_pool
            
            # Thêm số ngẫu nhiên cho đến khi đủ 5 số
            import random
            additional_numbers = random.sample(list(available_numbers), 
                                             min(5 - len(candidate_pool), len(available_numbers)))
            candidate_pool.update(additional_numbers)
        
        # Chọn 5 số theo thứ tự ưu tiên
        final_numbers = []
        
        # Ưu tiên 1: Model predictions (không bị loại trừ)
        for num in model_predictions:
            if num not in excluded_numbers and len(final_numbers) < 5:
                final_numbers.append(num)
        
        # Ưu tiên 2: Cold numbers (không bị loại trừ)
        for num in cold_numbers:
            if num not in excluded_numbers and num not in final_numbers and len(final_numbers) < 5:
                final_numbers.append(num)
        
        # Ưu tiên 3: Số ngẫu nhiên còn lại (nếu cần)
        if len(final_numbers) < 5:
            remaining_pool = candidate_pool - set(final_numbers)
            additional = list(remaining_pool)[:5-len(final_numbers)]
            final_numbers.extend(additional)
        
        return final_numbers[:5]

    def predict(self, day_results=None):
        """Main prediction function - trả về 5 số cuối cùng"""
        print("\n" + "="*50)
        print("🎯 SIMPLIFIED KENO PREDICTION")
        print("="*50)
        
        # Nếu không có day_results, load dữ liệu ngày hiện tại
        if not day_results:
            data = self.load_recent_data(100)
            if not data:
                print("❌ Cannot load data")
                return []
            
            # Lấy dữ liệu ngày hiện tại
            today = data[-1]['date']
            day_results = [draw['numbers'] for draw in data if draw['date'] == today]
            print(f"📅 Using data from: {today} ({len(day_results)} periods)")
        
        # 1. Lấy dự đoán từ LSTM model
        model_predictions = self.get_model_predictions(day_results)
        
        # 2. Lấy danh sách số bị loại bỏ
        excluded_numbers = self.get_excluded_numbers(day_results)
        
        # 3. Lấy top 10 số lạnh
        cold_numbers = self.get_cold_numbers(day_results)
        
        # 4. Chọn 5 số cuối cùng
        final_predictions = self.select_final_5_numbers(
            model_predictions, cold_numbers, excluded_numbers
        )
        
        print(f"\n🎯 FINAL PREDICTION: {final_predictions}")
        print("="*50)
        
        return final_predictions

def predict_5_numbers():
    """Dự đoán nhanh 5 số"""
    predictor = SimplifiedKenoPredictor()
    return predictor.predict()

def main():
    """Main function"""
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'predict':
            predictions = predict_5_numbers()
            if predictions:
                print(f"\n✅ 5 số dự đoán: {predictions}")
            else:
                print("\n❌ Không thể dự đoán")
        else:
            print("Sử dụng:")
            print("  python simplified_keno_predictor.py predict    # Dự đoán 5 số")
    else:
        # Mặc định: dự đoán
        predictions = predict_5_numbers()
        if predictions:
            print(f"\n✅ 5 số dự đoán: {predictions}")
        else:
            print("\n❌ Không thể dự đoán")

if __name__ == "__main__":
    main()
