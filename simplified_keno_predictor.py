#!/usr/bin/env python3
"""
Simplified Keno Predictor - Đơn gi<PERSON>n hóa hệ thống dự đoán
Chỉ bao gồm:
1. LSTM Model - dự đoán 5 số
2. <PERSON><PERSON><PERSON> hình loại bỏ số nóng
3. Top 10 số lạnh trong ngày
=> Chọn 5 số cuối cùng
"""

import numpy as np
import time
from datetime import datetime
from variable_length_model import VariableLengthKenoModel, connect_db

class SimplifiedKenoPredictor:
    """
    Simplified Predictor với 3 thành phần chính:
    1. LSTM Model predictions (5 số)
    2. Exclusion configuration (loại bỏ số nóng)
    3. Cold numbers detection (top 10 số lạnh)
    """

    def __init__(self, exclusion_threshold=3, exclusion_window=5, cold_number_count=10):
        self.lstm_model = None
        self.model_path = "keno_30_period_model.h5"

        # C<PERSON>u hình loại bỏ số
        self.exclusion_threshold = exclusion_threshold  # <PERSON><PERSON><PERSON> bỏ nếu xu<PERSON>t hiện >X lần
        self.exclusion_window = exclusion_window        # Trong Y kì gần nhất

        # C<PERSON>u hình số lạnh
        self.cold_number_count = cold_number_count      # Lấy top X số lạnh

        self.load_lstm_model()

    def load_lstm_model(self):
        """Load LSTM model"""
        try:
            import tensorflow as tf
            self.lstm_model = VariableLengthKenoModel()
            self.lstm_model.model = tf.keras.models.load_model(self.model_path)
            print("✅ LSTM model loaded successfully")
        except Exception as e:
            print(f"❌ Error loading LSTM model: {e}")
            self.lstm_model = None

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < 30:
                return None

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            return data

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None

    def get_model_predictions(self, day_results=None, num_predictions=5):
        """Lấy 5 số dự đoán từ LSTM model"""
        if not self.lstm_model:
            print("❌ LSTM model not available")
            return []

        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data or len(data) < 50:
                print("❌ Not enough data for LSTM prediction")
                return []
            day_results = [draw['numbers'] for draw in data[-50:]]

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for LSTM prediction")
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]
            print(f"🤖 LSTM predictions: {predictions}")
            return predictions

        except Exception as e:
            print(f"❌ LSTM prediction error: {e}")
            return []

    def get_excluded_numbers(self, day_results):
        """Lấy danh sách số bị loại bỏ (số nóng)"""
        if not day_results or len(day_results) < self.exclusion_window:
            return set()

        # Lấy exclusion_window kì gần nhất
        recent_draws = day_results[-self.exclusion_window:]
        excluded_numbers = set()

        # Đếm số lần xuất hiện của mỗi số
        for num in range(1, 81):
            appearance_count = 0
            for draw in recent_draws:
                if num in draw:
                    appearance_count += 1

            # Nếu xuất hiện quá exclusion_threshold lần, loại bỏ
            if appearance_count > self.exclusion_threshold:
                excluded_numbers.add(num)

        if excluded_numbers:
            print(f"🚫 Excluded numbers (hot): {sorted(list(excluded_numbers))}")
        else:
            print("✅ No numbers excluded")

        return excluded_numbers

    def get_cold_numbers(self, day_results):
        """Lấy top 10 số lạnh (ít xuất hiện nhất trong ngày hôm đó)"""
        if not day_results:
            return []

        # Đếm số lần xuất hiện của mỗi số trong ngày
        number_counts = {}
        for num in range(1, 81):
            count = 0
            for draw in day_results:
                if num in draw:
                    count += 1
            number_counts[num] = count

        # Sắp xếp theo số lần xuất hiện tăng dần (số lạnh nhất trước)
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1])

        # Lấy top cold_number_count số lạnh nhất
        cold_numbers = [num for num, count in sorted_numbers[:self.cold_number_count]]

        print(f"🧊 Top {self.cold_number_count} cold numbers: {cold_numbers}")
        return cold_numbers

    def select_final_5_numbers(self, model_predictions, cold_numbers, excluded_numbers):
        """Chọn 5 số cuối cùng từ model + cold numbers, loại trừ excluded numbers"""

        # Tạo pool ứng viên từ model predictions + cold numbers
        candidate_pool = set()

        # Thêm model predictions (ưu tiên cao)
        for num in model_predictions:
            if num not in excluded_numbers:
                candidate_pool.add(num)

        # Thêm cold numbers (ưu tiên thấp hơn)
        for num in cold_numbers:
            if num not in excluded_numbers:
                candidate_pool.add(num)

        # Nếu không đủ 5 số, thêm số ngẫu nhiên không bị loại trừ
        if len(candidate_pool) < 5:
            all_numbers = set(range(1, 81))
            available_numbers = all_numbers - excluded_numbers - candidate_pool

            # Thêm số ngẫu nhiên cho đến khi đủ 5 số
            import random
            additional_numbers = random.sample(list(available_numbers),
                                             min(5 - len(candidate_pool), len(available_numbers)))
            candidate_pool.update(additional_numbers)

        # Chọn 5 số theo thứ tự ưu tiên
        final_numbers = []

        # Ưu tiên 1: Model predictions (không bị loại trừ)
        for num in model_predictions:
            if num not in excluded_numbers and len(final_numbers) < 5:
                final_numbers.append(num)

        # Ưu tiên 2: Cold numbers (không bị loại trừ)
        for num in cold_numbers:
            if num not in excluded_numbers and num not in final_numbers and len(final_numbers) < 5:
                final_numbers.append(num)

        # Ưu tiên 3: Số ngẫu nhiên còn lại (nếu cần)
        if len(final_numbers) < 5:
            remaining_pool = candidate_pool - set(final_numbers)
            additional = list(remaining_pool)[:5-len(final_numbers)]
            final_numbers.extend(additional)

        return final_numbers[:5]

    def predict(self, day_results=None):
        """Main prediction function - trả về 5 số cuối cùng"""
        print("\n" + "="*50)
        print("🎯 SIMPLIFIED KENO PREDICTION")
        print("="*50)

        # Nếu không có day_results, load dữ liệu ngày hiện tại
        if not day_results:
            data = self.load_recent_data(100)
            if not data:
                print("❌ Cannot load data")
                return []

            # Lấy dữ liệu ngày hiện tại
            today = data[-1]['date']
            day_results = [draw['numbers'] for draw in data if draw['date'] == today]
            print(f"📅 Using data from: {today} ({len(day_results)} periods)")

        # 1. Lấy dự đoán từ LSTM model
        model_predictions = self.get_model_predictions(day_results)

        # 2. Lấy danh sách số bị loại bỏ
        excluded_numbers = self.get_excluded_numbers(day_results)

        # 3. Lấy top 10 số lạnh
        cold_numbers = self.get_cold_numbers(day_results)

        # 4. Chọn 5 số cuối cùng
        final_predictions = self.select_final_5_numbers(
            model_predictions, cold_numbers, excluded_numbers
        )

        print(f"\n🎯 FINAL PREDICTION: {final_predictions}")
        print("="*50)

        return final_predictions

    def get_test_dates(self):
        """Lấy danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            return [row['date'] for row in rows]

        except Exception as e:
            print(f"❌ Error getting test dates: {e}")
            return []

    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            print(f"❌ Error getting day draws: {e}")
            return []

    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0, 0, 0, [], []

        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)

        # Số dự đoán đúng và sai
        correct_numbers = list(predicted_set & actual_missing)  # Số dự đoán đúng
        wrong_numbers = list(predicted_set - actual_missing)    # Số dự đoán sai

        # Số dự đoán đúng
        correct_predictions = len(correct_numbers)

        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100

        return accuracy, correct_predictions, len(predicted_missing), correct_numbers, wrong_numbers

    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày từ kì 51"""
        print(f"\n=== Test ngày {test_date} ===")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        predictions_count = 0
        perfect_predictions = 0  # Số lần dự đoán đúng ≥4/5 số trượt
        good_predictions = 0     # Số lần dự đoán đúng 5/5 số trượt
        normal_predictions = 0   # Số lần dự đoán đúng 4/5 số trượt
        bad_predictions = 0      # Số lần dự đoán đúng ≤3/5 số trượt

        # Test từ kì 51 đến cuối ngày
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 5 số trượt
            try:
                predicted_missing = self.predict(day_results=input_draws)

                if predicted_missing:
                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']
                    actual_time = day_draws[draw_index]['time']

                    # Tính độ chính xác
                    _, correct, _, _, _ = self.calculate_accuracy(predicted_missing, actual_results)

                    predictions_count += 1

                    # Đếm các loại dự đoán
                    if correct >= 5:
                        good_predictions += 1  # 5/5 = Good
                        perfect_predictions += 1
                    elif correct >= 4:
                        normal_predictions += 1  # 4/5 = Normal
                        perfect_predictions += 1
                    else:
                        bad_predictions += 1  # ≤3/5 = Bad

                    # Hiển thị kết quả
                    label = "Good" if correct >= 5 else "Normal" if correct >= 4 else "Bad"
                    status_icon = "⭐" if correct >= 4 else "❌" if correct <= 2 else "⚠️"

                    print(f"Kì {draw_number:3d} ({actual_time}): {correct}/5 đúng - {label} {status_icon}")

            except Exception as e:
                print(f"❌ Error predicting period {draw_number}: {e}")

        # Tính độ chính xác cho cả ngày
        if predictions_count > 0:
            perfect_rate = (perfect_predictions / predictions_count) * 100
            good_rate = (good_predictions / predictions_count) * 100
            normal_rate = (normal_predictions / predictions_count) * 100
            bad_rate = (bad_predictions / predictions_count) * 100

            print(f"📊 {test_date}: Perfect(≥4/5): {perfect_predictions}/{predictions_count} = {perfect_rate:.1f}%")

            result = {
                'date': str(test_date),
                'predictions_count': predictions_count,
                'perfect_predictions': perfect_predictions,
                'perfect_rate': perfect_rate,
                'good_predictions': good_predictions,
                'good_rate': good_rate,
                'normal_predictions': normal_predictions,
                'normal_rate': normal_rate,
                'bad_predictions': bad_predictions,
                'bad_rate': bad_rate
            }

            return result

        return None

    def test_all_days(self):
        """Test tất cả ngày từ 2025-04-01"""
        print("=== Test độ chính xác dự đoán (từ 2025-04-01) ===")
        print("Sử dụng: Simplified Keno Predictor")
        print(f"- LSTM Model + Loại bỏ số nóng (>{self.exclusion_threshold} lần/{self.exclusion_window} kì)")
        print(f"- Top {self.cold_number_count} số lạnh")

        test_dates = self.get_test_dates()

        if not test_dates:
            print("Không có ngày nào để test")
            return

        print(f"Sẽ test {len(test_dates)} ngày")

        total_perfect_predictions = 0
        total_prediction_count = 0
        successful_days = 0
        daily_results = []

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}]", end=" ")

            day_result = self.test_single_day(test_date)

            if day_result:
                total_perfect_predictions += day_result['perfect_predictions']
                total_prediction_count += day_result['predictions_count']
                successful_days += 1
                daily_results.append(day_result)

        # Tổng kết
        if total_prediction_count > 0:
            overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100

            print(f"\n=== TỔNG KẾT ===")
            print(f"Dự đoán xuất sắc (≥4/5): {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.2f}%)")
            print(f"Số ngày test: {successful_days}/{len(test_dates)}")
            print(f"Phương pháp: Simplified Predictor (LSTM + Exclusion + Cold Numbers)")

            # Thống kê chi tiết
            self.print_daily_summary(daily_results, total_perfect_predictions, total_prediction_count)
        else:
            print("\nKhông có kết quả test nào")

    def print_daily_summary(self, daily_results, total_perfect_predictions, total_prediction_count):
        """In thống kê chi tiết các ngày"""
        print(f"\n" + "="*70)
        print("📊 THỐNG KÊ CHI TIẾT CÁC NGÀY")
        print("="*70)

        if not daily_results:
            print("Không có dữ liệu ngày để thống kê")
            return

        # Sắp xếp theo ngày
        sorted_results = sorted(daily_results, key=lambda x: x['date'])

        # Header bảng
        print(f"{'Ngày':<12} {'Good':<8} {'Normal':<8} {'≥4/5':<8} {'Bad':<8} {'Kì':<5}")
        print("-" * 60)

        # Tính tổng
        total_good = sum(r['good_predictions'] for r in sorted_results)
        total_normal = sum(r['normal_predictions'] for r in sorted_results)
        total_bad = sum(r['bad_predictions'] for r in sorted_results)

        # In từng ngày
        for result in sorted_results:
            date_str = str(result['date'])
            good_str = f"{result['good_predictions']}({result['good_rate']:.0f}%)"
            normal_str = f"{result['normal_predictions']}({result['normal_rate']:.0f}%)"
            perfect_str = f"{result['perfect_predictions']}({result['perfect_rate']:.0f}%)"
            bad_str = f"{result['bad_predictions']}({result['bad_rate']:.0f}%)"
            predictions_str = f"{result['predictions_count']}"

            print(f"{date_str:<12} {good_str:<8} {normal_str:<8} {perfect_str:<8} {bad_str:<8} {predictions_str:<5}")

        # Thống kê tổng hợp
        print("-" * 60)
        overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100
        overall_good_rate = (total_good / total_prediction_count) * 100
        overall_normal_rate = (total_normal / total_prediction_count) * 100
        overall_bad_rate = (total_bad / total_prediction_count) * 100

        good_total_str = f"{total_good}({overall_good_rate:.0f}%)"
        normal_total_str = f"{total_normal}({overall_normal_rate:.0f}%)"
        perfect_total_str = f"{total_perfect_predictions}({overall_perfect_rate:.0f}%)"
        bad_total_str = f"{total_bad}({overall_bad_rate:.0f}%)"

        print(f"{'TỔNG':<12} {good_total_str:<8} {normal_total_str:<8} {perfect_total_str:<8} {bad_total_str:<8} {total_prediction_count:<5}")

        print(f"\n📈 PHÂN TÍCH CHI TIẾT:")
        print(f"   • 5/5 đúng (Good):      {total_good}/{total_prediction_count} lần ({overall_good_rate:.1f}%)")
        print(f"   • 4/5 đúng (Normal):    {total_normal}/{total_prediction_count} lần ({overall_normal_rate:.1f}%)")
        print(f"   • ≥4/5 đúng (Perfect):  {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.1f}%)")
        print(f"   • ≤3/5 đúng (Bad):      {total_bad}/{total_prediction_count} lần ({overall_bad_rate:.1f}%)")

        print("="*70)

def predict_5_numbers():
    """Dự đoán nhanh 5 số"""
    predictor = SimplifiedKenoPredictor()
    return predictor.predict()

def test_from_2025_04_01():
    """Test với dữ liệu từ 2025-04-01"""
    predictor = SimplifiedKenoPredictor()
    predictor.test_all_days()

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'predict':
            predictions = predict_5_numbers()
            if predictions:
                print(f"\n✅ 5 số dự đoán: {predictions}")
            else:
                print("\n❌ Không thể dự đoán")
        elif mode == 'test':
            test_from_2025_04_01()
        else:
            print("Sử dụng:")
            print("  python simplified_keno_predictor.py predict    # Dự đoán 5 số")
            print("  python simplified_keno_predictor.py test       # Test từ 2025-04-01")
    else:
        # Mặc định: dự đoán
        predictions = predict_5_numbers()
        if predictions:
            print(f"\n✅ 5 số dự đoán: {predictions}")
        else:
            print("\n❌ Không thể dự đoán")

if __name__ == "__main__":
    main()
