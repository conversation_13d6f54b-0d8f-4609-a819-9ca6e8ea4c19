#!/usr/bin/env python3
"""
Simplified Keno Predictor - Tập trung dự đoán 4/4 chính xác
Chỉ bao gồm:
1. Statistical Model - dự đoán 5 số trượt
2. Lo<PERSON>i bỏ tạm thời số xuất hiện liên tục
3. Tạo 1 bộ 4 số tối ưu từ 5 số
=> Tập trung 100% vào 4/4 chính xác
"""

import numpy as np
import time
from datetime import datetime
from variable_length_model import VariableLengthKenoModel, connect_db

class SimplifiedKenoPredictor:
    """
    FOCUS ON 4/4 ACCURACY - Tập trung 100% vào dự đoán 4/4 chính xác
    1. Missing Numbers Pattern Analysis
    2. Consecutive Numbers Exclusion System
    3. Statistical Frequency Analysis
    4. Single Best 4-Number Combination from 5 numbers
    """

    def __init__(self):
        # Bỏ LSTM model - chuy<PERSON>n sang statistical approach
        self.lstm_model = None

        # Cấu hình phân tích pattern số trượt
        self.missing_analysis_window = 50  # Phân tích 50 kì gần nhất
        self.pattern_depth = 100          # Độ sâu phân tích pattern

        # Cấu hình statistical thresholds
        self.frequency_threshold = 0.3    # Ngưỡng tần suất xuất hiện
        self.pattern_confidence = 0.7     # Độ tin cậy pattern

        # CẤU HÌNH MỚI: Loại bỏ tạm thời số liên tục
        self.consecutive_exclusion_window = 3  # X = 3 kì liên tục
        self.consecutive_recovery_period = 5   # Y = 5 kì để dự đoán lại

        # Database tracking consecutive appearances
        self.consecutive_tracker = {}  # {number: {'count': X, 'excluded_since': period}}
        self.excluded_numbers_history = {}  # Track excluded numbers and recovery time

        # Database của missing numbers patterns
        self.missing_patterns_db = {}

        # CẤU HÌNH CHIẾN LƯỢC MARTINGALE
        self.martingale_config = {
            'base_bet': 10000,           # Cược cơ bản 10k
            'max_level': 7,              # Tối đa 7 level gấp thếp
            'multiplier': 2.2,           # Hệ số nhân 2.2 (thay vì 2.0)
            'win_target': 22000,         # Mục tiêu lãi mỗi chu kỳ (32k - 10k)
            'stop_loss': 1000000,        # Dừng lỗ tại 1 triệu
            'reset_after_win': True      # Reset về base sau khi thắng
        }

        # Tracking Martingale state
        self.current_martingale_level = 1
        self.total_invested_in_cycle = 0
        self.session_profit = 0
        self.consecutive_losses = 0

        print("✅ ADVANCED Statistical Keno Predictor - FOCUS ON 4/4 ACCURACY")
        print(f"   📊 Input analysis: {self.missing_analysis_window} periods")
        print(f"   🚫 Consecutive exclusion: {self.consecutive_exclusion_window} periods")
        print(f"   🔄 Recovery period: {self.consecutive_recovery_period} periods")
        print(f"   💰 Martingale: Base {self.martingale_config['base_bet']:,}k, Max level {self.martingale_config['max_level']}")

    def track_consecutive_appearances(self, day_results):
        """Theo dõi số xuất hiện liên tục và cập nhật danh sách loại bỏ tạm thời"""
        if not day_results or len(day_results) < self.consecutive_exclusion_window:
            return set()

        # Reset tracker cho phiên mới
        current_period = len(day_results)

        # Kiểm tra từng số từ 1-80
        for num in range(1, 81):
            consecutive_count = 0

            # Đếm số kì liên tục xuất hiện gần nhất
            for i in range(len(day_results) - 1, -1, -1):
                if num in day_results[i]:
                    consecutive_count += 1
                else:
                    break

            # Cập nhật tracker
            if consecutive_count >= self.consecutive_exclusion_window:
                # Số này xuất hiện liên tục >= X kì
                if num not in self.consecutive_tracker:
                    self.consecutive_tracker[num] = {
                        'count': consecutive_count,
                        'excluded_since': current_period
                    }
                    print(f"🚫 Number {num} excluded (appeared {consecutive_count} consecutive periods)")
                else:
                    # Cập nhật count
                    self.consecutive_tracker[num]['count'] = consecutive_count
            else:
                # Số này không còn xuất hiện liên tục
                if num in self.consecutive_tracker:
                    del self.consecutive_tracker[num]
                    print(f"✅ Number {num} recovered (no longer consecutive)")

    def get_temporarily_excluded_numbers(self, current_period):
        """Lấy danh sách số bị loại bỏ tạm thời"""
        excluded = set()

        # Kiểm tra từng số trong tracker
        numbers_to_remove = []
        for num, info in self.consecutive_tracker.items():
            periods_excluded = current_period - info['excluded_since']

            if periods_excluded >= self.consecutive_recovery_period:
                # Đã đủ Y kì để recovery
                numbers_to_remove.append(num)
                print(f"🔄 Number {num} ready for recovery after {periods_excluded} periods")
            else:
                # Vẫn trong thời gian loại bỏ
                excluded.add(num)

        # Xóa các số đã recovery
        for num in numbers_to_remove:
            del self.consecutive_tracker[num]

        if excluded:
            print(f"🚫 Temporarily excluded numbers: {sorted(list(excluded))}")

        return excluded

    def calculate_martingale_bet(self):
        """Tính toán số tiền cược theo chiến lược Martingale"""
        base_bet = self.martingale_config['base_bet']
        multiplier = self.martingale_config['multiplier']
        level = self.current_martingale_level

        # Tính số tiền cược cho level hiện tại
        current_bet = base_bet * (multiplier ** (level - 1))

        return int(current_bet)

    def update_martingale_after_result(self, is_win, actual_bet_amount):
        """Cập nhật trạng thái Martingale sau kết quả"""
        if is_win:
            # THẮNG: Reset về level 1
            win_amount = 32000  # Thưởng 32k cho 4/4 đúng
            profit = win_amount - actual_bet_amount
            self.session_profit += profit
            self.total_invested_in_cycle = 0
            self.current_martingale_level = 1
            self.consecutive_losses = 0

            print(f"🎉 WIN! Profit: +{profit:,}k | Session total: {self.session_profit:,}k")
            print(f"🔄 Martingale RESET to level 1")

        else:
            # THUA: Tăng level
            self.session_profit -= actual_bet_amount
            self.total_invested_in_cycle += actual_bet_amount
            self.consecutive_losses += 1

            # Kiểm tra stop loss
            if self.total_invested_in_cycle >= self.martingale_config['stop_loss']:
                print(f"🛑 STOP LOSS reached! Total loss: {self.total_invested_in_cycle:,}k")
                print(f"🔄 Resetting Martingale to level 1")
                self.current_martingale_level = 1
                self.total_invested_in_cycle = 0
                self.consecutive_losses = 0
            elif self.current_martingale_level < self.martingale_config['max_level']:
                self.current_martingale_level += 1
                print(f"💸 LOSS! Level up to {self.current_martingale_level}")
                print(f"📊 Cycle invested: {self.total_invested_in_cycle:,}k | Session: {self.session_profit:,}k")
            else:
                print(f"⚠️ MAX LEVEL {self.martingale_config['max_level']} reached!")
                print(f"🔄 Resetting to level 1 (cut loss)")
                self.current_martingale_level = 1
                self.total_invested_in_cycle = 0
                self.consecutive_losses = 0

    def get_martingale_recommendation(self):
        """Đưa ra khuyến nghị Martingale cho lần cược tiếp theo"""
        current_bet = self.calculate_martingale_bet()

        # Tính toán potential profit nếu thắng
        potential_win = 32000 - current_bet
        potential_cycle_profit = potential_win - self.total_invested_in_cycle

        recommendation = {
            'level': self.current_martingale_level,
            'bet_amount': current_bet,
            'total_cycle_invested': self.total_invested_in_cycle,
            'potential_win': potential_win,
            'potential_cycle_profit': potential_cycle_profit,
            'session_profit': self.session_profit,
            'consecutive_losses': self.consecutive_losses,
            'risk_level': 'LOW' if self.current_martingale_level <= 2 else 'MEDIUM' if self.current_martingale_level <= 4 else 'HIGH'
        }

        return recommendation

    def analyze_missing_numbers_history(self, day_results):
        """Phân tích lịch sử số trượt để tìm pattern"""
        if not day_results or len(day_results) < self.missing_analysis_window:
            return {}

        # Lấy dữ liệu gần nhất
        recent_draws = day_results[-self.missing_analysis_window:]

        # Tạo database số trượt cho mỗi kì
        missing_history = []
        for draw in recent_draws:
            missing_numbers = set(range(1, 81)) - set(draw)
            missing_history.append(list(missing_numbers))

        # Phân tích tần suất số trượt
        missing_frequency = {}
        for num in range(1, 81):
            count = sum(1 for missing_set in missing_history if num in missing_set)
            frequency = count / len(missing_history)
            missing_frequency[num] = frequency

        print(f"📊 MISSING NUMBERS ANALYSIS ({len(missing_history)} periods):")

        # Sắp xếp theo tần suất trượt giảm dần
        sorted_missing = sorted(missing_frequency.items(), key=lambda x: x[1], reverse=True)

        # Top 10 số trượt nhiều nhất
        top_missing = sorted_missing[:10]
        print(f"   Top 10 most missing: {[f'{num}({freq:.2f})' for num, freq in top_missing]}")

        return missing_frequency

    def analyze_missing_patterns_by_time(self, day_results):
        """Phân tích pattern số trượt theo thời gian"""
        if not day_results or len(day_results) < 20:
            return {}

        # Phân tích theo chu kỳ (mỗi 5 kì)
        cycle_patterns = {}

        for i in range(0, len(day_results) - 4, 5):
            cycle_draws = day_results[i:i+5]
            cycle_missing = []

            for draw in cycle_draws:
                missing = set(range(1, 81)) - set(draw)
                cycle_missing.extend(list(missing))

            # Đếm tần suất trong chu kỳ này
            cycle_freq = {}
            for num in range(1, 81):
                count = cycle_missing.count(num)
                cycle_freq[num] = count / len(cycle_missing) if cycle_missing else 0

            cycle_patterns[f"cycle_{i//5}"] = cycle_freq

        return cycle_patterns

    def find_best_missing_candidates(self, day_results):
        """Tìm 5 số có khả năng trượt cao nhất dựa trên statistical analysis"""
        print(f"\n🔍 STATISTICAL MISSING NUMBERS ANALYSIS:")

        # 1. Theo dõi và loại bỏ số xuất hiện liên tục
        self.track_consecutive_appearances(day_results)
        current_period = len(day_results)
        temporarily_excluded = self.get_temporarily_excluded_numbers(current_period)

        # 2. Phân tích tần suất trượt lịch sử
        missing_freq = self.analyze_missing_numbers_history(day_results)

        # 3. Phân tích pattern theo thời gian
        time_patterns = self.analyze_missing_patterns_by_time(day_results)

        # 4. Phân tích xu hướng gần đây (10 kì cuối)
        recent_trend = self.analyze_recent_missing_trend(day_results[-10:] if len(day_results) >= 10 else day_results)

        # 5. Tính điểm tổng hợp cho mỗi số (loại bỏ số bị excluded tạm thời)
        candidate_scores = {}

        for num in range(1, 81):
            # Bỏ qua số bị loại bỏ tạm thời
            if num in temporarily_excluded:
                continue

            score = 0.0

            # Điểm từ tần suất lịch sử (40%)
            if num in missing_freq:
                score += missing_freq[num] * 0.4

            # Điểm từ xu hướng gần đây (35%)
            if num in recent_trend:
                score += recent_trend[num] * 0.35

            # Điểm từ pattern analysis (25%)
            pattern_score = self.calculate_pattern_score(num, time_patterns)
            score += pattern_score * 0.25

            candidate_scores[num] = score

        # Sắp xếp theo điểm giảm dần và chọn top 5
        sorted_candidates = sorted(candidate_scores.items(), key=lambda x: x[1], reverse=True)

        top_5_candidates = [num for num, _ in sorted_candidates[:5]]

        print(f"🎯 TOP 5 MISSING CANDIDATES:")
        for i, (num, score) in enumerate(sorted_candidates[:5], 1):
            print(f"   {i}. Number {num}: score {score:.3f}")

        return top_5_candidates, candidate_scores

    def analyze_recent_missing_trend(self, recent_draws):
        """Phân tích xu hướng trượt gần đây"""
        if not recent_draws:
            return {}

        trend_scores = {}

        # Tính trọng số giảm dần cho các kì gần đây
        weights = [1.0, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1]

        for i, draw in enumerate(reversed(recent_draws)):
            weight = weights[i] if i < len(weights) else 0.1
            missing_numbers = set(range(1, 81)) - set(draw)

            for num in missing_numbers:
                if num not in trend_scores:
                    trend_scores[num] = 0.0
                trend_scores[num] += weight

        # Normalize scores
        max_score = max(trend_scores.values()) if trend_scores else 1
        for num in trend_scores:
            trend_scores[num] = trend_scores[num] / max_score

        return trend_scores

    def calculate_pattern_score(self, number, time_patterns):
        """Tính điểm pattern cho một số"""
        if not time_patterns:
            return 0.0

        total_score = 0.0
        pattern_count = 0

        for cycle, pattern in time_patterns.items():
            if number in pattern:
                total_score += pattern[number]
                pattern_count += 1

        return total_score / pattern_count if pattern_count > 0 else 0.0

    def select_best_4_from_5(self, five_candidates, candidate_scores):
        """Chọn 1 bộ 4 số tốt nhất từ 5 số ứng viên - FOCUS ON 4/4 ACCURACY"""
        from itertools import combinations

        print(f"\n🎯 SELECTING BEST 4-NUMBER COMBINATION:")
        print(f"   From 5 candidates: {five_candidates}")

        # Tạo tất cả các tổ hợp 4 số từ 5 số (chỉ có 5 bộ)
        all_combinations = list(combinations(five_candidates, 4))

        print(f"   Total possible combinations: {len(all_combinations)}")

        # Tính điểm cho mỗi bộ 4 số
        combination_scores = []

        for combo in all_combinations:
            # Điểm = tổng điểm của 4 số
            combo_score = sum(candidate_scores[num] for num in combo)

            # Bonus cho sự cân bằng điểm (tối ưu cho 4/4 accuracy)
            scores_list = [candidate_scores[num] for num in combo]
            score_variance = np.var(scores_list)
            balance_bonus = 0.15 / (1 + score_variance)  # Tăng bonus cho điểm đều nhau

            # Bonus cho số có điểm cao (ưu tiên top candidates)
            high_score_bonus = sum(0.05 for score in scores_list if score > 0.5)

            total_score = combo_score + balance_bonus + high_score_bonus
            combination_scores.append((combo, total_score))

        # Sắp xếp theo điểm giảm dần
        combination_scores.sort(key=lambda x: x[1], reverse=True)

        # Chọn bộ tốt nhất
        best_combo, best_score = combination_scores[0]

        print(f"🏆 BEST 4-NUMBER COMBINATION (OPTIMIZED FOR 4/4):")
        print(f"   Numbers: {list(best_combo)}")
        print(f"   Total Score: {best_score:.4f}")
        print(f"   Individual Scores: {[f'{num}({candidate_scores[num]:.3f})' for num in best_combo]}")

        # Hiển thị tất cả 5 bộ để so sánh
        print(f"\n📊 ALL 5 COMBINATIONS:")
        for i, (combo, score) in enumerate(combination_scores, 1):
            print(f"   {i}. {list(combo)} - Score: {score:.4f}")

        return list(best_combo)

    def predict_statistical(self, day_results=None):
        """Main prediction function - Statistical approach - FOCUS ON 4/4 ACCURACY"""
        print("\n" + "="*60)
        print("🎯 STATISTICAL KENO PREDICTION - 4/4 ACCURACY FOCUS")
        print("="*60)

        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(200)  # Load nhiều hơn để phân tích
            if not data:
                print("❌ Cannot load data")
                return [], []

            # Sử dụng tất cả dữ liệu có sẵn
            day_results = [draw['numbers'] for draw in data]
            print(f"📅 Using {len(day_results)} periods for statistical analysis")

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for statistical analysis")
            return [], []

        # 1. Tìm 5 số có khả năng trượt cao nhất (với consecutive exclusion)
        five_candidates, candidate_scores = self.find_best_missing_candidates(day_results)

        # 2. Chọn 1 bộ 4 số tốt nhất từ 5 số
        best_4_combo = self.select_best_4_from_5(five_candidates, candidate_scores)

        # 3. Đưa ra khuyến nghị Martingale
        martingale_rec = self.get_martingale_recommendation()

        print(f"\n🎯 FINAL STATISTICAL PREDICTION:")
        print(f"   5 Missing Candidates: {five_candidates}")
        print(f"   Best 4-Number Combo: {best_4_combo}")

        print(f"\n💰 MARTINGALE STRATEGY RECOMMENDATION:")
        print(f"   Level: {martingale_rec['level']}/7 ({martingale_rec['risk_level']} risk)")
        print(f"   Bet Amount: {martingale_rec['bet_amount']:,}k")
        print(f"   Cycle Invested: {martingale_rec['total_cycle_invested']:,}k")
        print(f"   Potential Win: +{martingale_rec['potential_win']:,}k")
        print(f"   Potential Cycle Profit: {martingale_rec['potential_cycle_profit']:,}k")
        print(f"   Session Profit: {martingale_rec['session_profit']:,}k")
        print(f"   Consecutive Losses: {martingale_rec['consecutive_losses']}")

        if martingale_rec['risk_level'] == 'HIGH':
            print(f"   ⚠️ WARNING: High risk level! Consider smaller bet or skip.")
        elif martingale_rec['risk_level'] == 'MEDIUM':
            print(f"   🔸 CAUTION: Medium risk level. Bet carefully.")
        else:
            print(f"   ✅ SAFE: Low risk level. Good to bet.")

        print("="*60)

        return five_candidates, [best_4_combo]

    def load_recent_data(self, limit_draws=100):
        """Load dữ liệu gần nhất"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT date, time, results, period
                FROM histories_keno
                ORDER BY date DESC, time DESC
                LIMIT %s
            """

            cursor.execute(query, (limit_draws,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            if len(rows) < 30:
                return None

            # Chuyển đổi dữ liệu và reverse để có thứ tự từ cũ đến mới
            data = []
            for row in reversed(rows):
                numbers = [int(n) for n in row['results'].split(',')]
                data.append({
                    'date': row['date'],
                    'time': row['time'],
                    'numbers': numbers,
                    'period': row['period']
                })

            return data

        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return None

    def get_model_predictions(self, day_results=None, num_predictions=10):
        """Lấy 10 số trượt cao nhất từ LSTM model"""
        if not self.lstm_model:
            print("❌ LSTM model not available")
            return []

        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data or len(data) < 50:
                print("❌ Not enough data for LSTM prediction")
                return []
            day_results = [draw['numbers'] for draw in data[-50:]]

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for LSTM prediction")
            return []

        try:
            # Dự đoán bằng LSTM
            probabilities = self.lstm_model.predict_next_draw(day_results)

            if probabilities is None:
                return []

            # Chuyển đổi probabilities thành missing numbers
            # Số có xác suất thấp = có khả năng trượt cao
            number_probs = [(i+1, prob) for i, prob in enumerate(probabilities)]
            number_probs.sort(key=lambda x: x[1])  # Sắp xếp theo xác suất tăng dần

            predictions = [num for num, _ in number_probs[:num_predictions]]
            print(f"🤖 LSTM top {num_predictions} predictions: {predictions}")
            return predictions

        except Exception as e:
            print(f"❌ LSTM prediction error: {e}")
            return []

    def get_excluded_numbers(self, day_results):
        """Lấy danh sách số bị loại bỏ (số nóng)"""
        if not day_results or len(day_results) < self.exclusion_window:
            return set()

        # Lấy exclusion_window kì gần nhất
        recent_draws = day_results[-self.exclusion_window:]
        excluded_numbers = set()

        # Đếm số lần xuất hiện của mỗi số
        for num in range(1, 81):
            appearance_count = 0
            for draw in recent_draws:
                if num in draw:
                    appearance_count += 1

            # Nếu xuất hiện quá exclusion_threshold lần, loại bỏ
            if appearance_count > self.exclusion_threshold:
                excluded_numbers.add(num)

        if excluded_numbers:
            print(f"🚫 Excluded numbers (hot): {sorted(list(excluded_numbers))}")
        else:
            print("✅ No numbers excluded")

        return excluded_numbers

    def get_hot_cold_numbers(self, day_results=None):
        """Lấy 10 hot numbers và 10 cold numbers từ 50 kì gần nhất"""
        # Nếu không có day_results, load dữ liệu gần nhất
        if not day_results:
            data = self.load_recent_data(100)
            if not data or len(data) < 50:
                print("❌ Not enough data for hot/cold analysis")
                return [], []
            day_results = [draw['numbers'] for draw in data[-50:]]

        if len(day_results) < 50:
            print("❌ Need at least 50 periods for hot/cold analysis")
            return [], []

        # Lấy 50 kì gần nhất
        recent_50_periods = day_results[-50:]

        # Đếm số lần xuất hiện của mỗi số trong 50 kì gần nhất
        number_counts = {}
        for num in range(1, 81):
            count = 0
            for draw in recent_50_periods:
                if num in draw:
                    count += 1
            number_counts[num] = count

        # Sắp xếp theo số lần xuất hiện
        sorted_numbers = sorted(number_counts.items(), key=lambda x: x[1])

        # Top 10 cold numbers (ít xuất hiện nhất)
        cold_numbers = [num for num, count in sorted_numbers[:10]]

        # Top 10 hot numbers (xuất hiện nhiều nhất)
        hot_numbers = [num for num, count in sorted_numbers[-10:]]
        hot_numbers.reverse()  # Sắp xếp từ nóng nhất đến ít nóng nhất

        print(f"🔥 Top 10 hot numbers (50 kì): {hot_numbers}")
        print(f"🧊 Top 10 cold numbers (50 kì): {cold_numbers}")

        return hot_numbers, cold_numbers

    def analyze_matches_and_prioritize(self, lstm_predictions, hot_numbers, cold_numbers):
        """
        CHIẾN LƯỢC MỚI: Tránh 3 top số nóng đầu tiên và top 2 số lạnh
        Logic: Số quá nóng hoặc quá lạnh thường có xu hướng nghỉ ngơi
        """

        # Định nghĩa các nhóm số cần tránh
        avoid_hot = hot_numbers[:3]      # Top 3 số nóng nhất - TRÁNH
        avoid_cold = cold_numbers[:2]    # Top 2 số lạnh nhất - TRÁNH

        # Định nghĩa các nhóm số ưu tiên
        good_hot = hot_numbers[3:7]      # Số 4-7 hot - ưu tiên cao (vẫn nóng nhưng không quá)
        good_cold = cold_numbers[2:6]    # Số 3-6 cold - ưu tiên trung bình (bắt đầu lạnh)
        neutral_hot = hot_numbers[7:10]  # Số 8-10 hot - ưu tiên trung bình
        neutral_cold = cold_numbers[6:10] # Số 7-10 cold - ưu tiên thấp

        print(f"🎯 CHIẾN LƯỢC TRÁNH SỐ:")
        print(f"   🚫 TRÁNH - Top 3 hot: {avoid_hot}")
        print(f"   🚫 TRÁNH - Top 2 cold: {avoid_cold}")
        print(f"   ⭐ ƯU TIÊN - Good hot (4-7): {good_hot}")
        print(f"   🔸 TRUNG BÌNH - Good cold (3-6): {good_cold}")
        print(f"   🔹 TRUNG BÌNH - Neutral hot (8-10): {neutral_hot}")
        print(f"   ➖ THẤP - Neutral cold (7-10): {neutral_cold}")

        # Phân loại LSTM predictions
        high_priority = []    # Ưu tiên cao nhất
        medium_priority = []  # Ưu tiên trung bình
        low_priority = []     # Ưu tiên thấp
        avoid_numbers = []    # Số cần tránh

        for num in lstm_predictions:
            if num in avoid_hot:
                # Top 3 hot => TRÁNH HOÀN TOÀN
                avoid_numbers.append(num)
                print(f"   🚫 {num} in top 3 hot - AVOID COMPLETELY")
            elif num in avoid_cold:
                # Top 2 cold => TRÁNH HOÀN TOÀN
                avoid_numbers.append(num)
                print(f"   🚫 {num} in top 2 cold - AVOID COMPLETELY")
            elif num in good_hot:
                # Số 4-7 hot => ưu tiên cao nhất
                high_priority.append(num)
                print(f"   ⭐ {num} in good hot range (4-7) - HIGH priority")
            elif num in good_cold:
                # Số 3-6 cold => ưu tiên trung bình
                medium_priority.append(num)
                print(f"   🔸 {num} in good cold range (3-6) - MEDIUM priority")
            elif num in neutral_hot:
                # Số 8-10 hot => ưu tiên trung bình
                medium_priority.append(num)
                print(f"   🔹 {num} in neutral hot range (8-10) - MEDIUM priority")
            elif num in neutral_cold:
                # Số 7-10 cold => ưu tiên thấp
                low_priority.append(num)
                print(f"   ➖ {num} in neutral cold range (7-10) - LOW priority")
            else:
                # Không thuộc hot/cold top 10 => ưu tiên cao nhất (số cân bằng)
                high_priority.append(num)
                print(f"   ✅ {num} balanced number - HIGH priority")

        # Kết hợp theo thứ tự ưu tiên (BỎ QUA avoid_numbers)
        prioritized_numbers = high_priority + medium_priority + low_priority

        print(f"🎯 Final prioritized order: {prioritized_numbers}")
        print(f"🚫 Avoided numbers: {avoid_numbers}")

        return prioritized_numbers, avoid_numbers

    def select_final_6_numbers(self, lstm_predictions, hot_numbers, cold_numbers, excluded_numbers):
        """Chọn 6 số cuối cùng với chiến lược tránh số mới"""

        print(f"\n🔄 FINAL SELECTION PROCESS:")
        print(f"   LSTM predictions: {lstm_predictions}")
        print(f"   Excluded numbers: {sorted(list(excluded_numbers))}")

        # Bước 1: Phân tích matches và ưu tiên (trả về cả avoid_numbers)
        prioritized_lstm, avoid_numbers = self.analyze_matches_and_prioritize(lstm_predictions, hot_numbers, cold_numbers)

        # Bước 2: Kết hợp excluded_numbers với avoid_numbers
        all_excluded = excluded_numbers.union(set(avoid_numbers))
        print(f"   All excluded (original + avoided): {sorted(list(all_excluded))}")

        # Bước 3: Loại bỏ tất cả số bị excluded và avoided
        valid_candidates = []
        for num in prioritized_lstm:
            if num not in all_excluded:
                valid_candidates.append(num)

        print(f"   Valid candidates (after all exclusions): {valid_candidates}")

        # Bước 4: Chọn 6 số đầu tiên
        final_numbers = valid_candidates[:6]

        # Bước 5: Nếu không đủ 6 số, bổ sung từ good hot numbers (4-7)
        if len(final_numbers) < 6:
            print(f"   Need {6 - len(final_numbers)} more numbers, adding from good hot (4-7)...")
            good_hot = hot_numbers[3:7]  # Số 4-7 hot
            for num in good_hot:
                if num not in all_excluded and num not in final_numbers and len(final_numbers) < 6:
                    final_numbers.append(num)
                    print(f"   + Added good hot number: {num}")

        # Bước 6: Nếu vẫn không đủ, bổ sung từ neutral numbers
        if len(final_numbers) < 6:
            print(f"   Still need {6 - len(final_numbers)} more numbers, adding neutral...")
            all_numbers = set(range(1, 81))
            hot_cold_set = set(hot_numbers + cold_numbers)
            neutral_numbers = list(all_numbers - hot_cold_set - all_excluded - set(final_numbers))

            needed = min(6 - len(final_numbers), len(neutral_numbers))
            if needed > 0:
                import random
                additional_numbers = random.sample(neutral_numbers, needed)
                final_numbers.extend(additional_numbers)
                print(f"   + Added neutral numbers: {additional_numbers}")

        # Bước 7: Cuối cùng nếu vẫn không đủ, bổ sung bất kỳ số nào còn lại
        if len(final_numbers) < 6:
            print(f"   Emergency: need {6 - len(final_numbers)} more numbers...")
            all_numbers = set(range(1, 81))
            available_numbers = all_numbers - set(final_numbers)

            needed = min(6 - len(final_numbers), len(available_numbers))
            if needed > 0:
                import random
                emergency_numbers = random.sample(list(available_numbers), needed)
                final_numbers.extend(emergency_numbers)
                print(f"   + Emergency numbers: {emergency_numbers}")

        return final_numbers[:6]

    def get_time_performance_level(self, current_time=None):
        """Xác định mức độ hiệu suất của khung giờ hiện tại"""
        if not current_time:
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M")

        # Chỉ lấy giờ:phút, bỏ giây
        time_str = current_time[:5]

        if time_str in self.time_optimization['high_performance']:
            return 'high', 1.2  # Boost 20%
        elif time_str in self.time_optimization['medium_performance']:
            return 'medium', 1.0  # Bình thường
        else:
            return 'low', 0.8  # Giảm 20%

    def optimize_prediction_by_time(self, six_numbers, hot_numbers, cold_numbers, current_time=None):
        """Tối ưu dự đoán dựa trên khung giờ"""
        performance_level, multiplier = self.get_time_performance_level(current_time)

        print(f"⏰ TIME OPTIMIZATION:")
        print(f"   Current time: {current_time or 'Now'}")
        print(f"   Performance level: {performance_level.upper()} (multiplier: {multiplier})")

        if performance_level == 'high':
            # Khung giờ tốt: Tăng số lượng bộ, ưu tiên bộ có điểm cao
            return self.generate_high_performance_combinations(six_numbers, hot_numbers, cold_numbers)
        elif performance_level == 'medium':
            # Khung giờ trung bình: Sử dụng smart combinations bình thường
            return self.generate_4_number_combinations_smart(six_numbers, hot_numbers, cold_numbers)
        else:
            # Khung giờ thấp: Chọn ít bộ hơn, thận trọng hơn
            return self.generate_conservative_combinations(six_numbers, hot_numbers, cold_numbers)

    def generate_high_performance_combinations(self, six_numbers, hot_numbers, cold_numbers):
        """Tạo bộ số cho khung giờ hiệu suất cao"""
        from itertools import combinations

        print(f"🔥 HIGH PERFORMANCE MODE - Aggressive Strategy")

        # Tạo tất cả 15 bộ có thể
        all_combinations = list(combinations(six_numbers, 4))

        # Tính điểm cho tất cả bộ
        scored_combinations = []
        for combo in all_combinations:
            score = self.calculate_combination_score(combo, hot_numbers, cold_numbers)
            # Boost điểm cho khung giờ tốt
            boosted_score = score * 1.2
            scored_combinations.append((combo, boosted_score))

        # Sắp xếp và chọn top 12 bộ (thay vì 10)
        scored_combinations.sort(key=lambda x: x[1], reverse=True)
        selected_combinations = [combo for combo, _ in scored_combinations[:12]]

        print(f"   Selected {len(selected_combinations)} AGGRESSIVE combinations")
        for i, (combo, score) in enumerate(scored_combinations[:12], 1):
            print(f"     Bộ {i:2d}: {list(combo)} (boosted score: {score:.2f})")

        return selected_combinations

    def generate_conservative_combinations(self, six_numbers, hot_numbers, cold_numbers):
        """Tạo bộ số cho khung giờ hiệu suất thấp"""
        from itertools import combinations

        print(f"⚠️ LOW PERFORMANCE MODE - Conservative Strategy")

        # Tạo tất cả bộ có thể
        all_combinations = list(combinations(six_numbers, 4))

        # Tính điểm với tiêu chí thận trọng hơn
        scored_combinations = []
        for combo in all_combinations:
            score = self.calculate_combination_score(combo, hot_numbers, cold_numbers)
            # Chỉ chọn bộ có điểm thực sự cao
            if score >= 8.0:  # Ngưỡng cao hơn
                scored_combinations.append((combo, score))

        # Sắp xếp và chỉ chọn top 6-8 bộ tốt nhất
        scored_combinations.sort(key=lambda x: x[1], reverse=True)
        selected_combinations = [combo for combo, _ in scored_combinations[:6]]

        print(f"   Selected {len(selected_combinations)} CONSERVATIVE combinations")
        for i, (combo, score) in enumerate(scored_combinations[:6], 1):
            print(f"     Bộ {i:2d}: {list(combo)} (score: {score:.2f})")

        return selected_combinations if selected_combinations else [six_numbers[:4]]  # Fallback

    def generate_4_number_combinations_smart(self, six_numbers, hot_numbers, cold_numbers):
        """Tạo bộ 4 số thông minh từ 6 số, ưu tiên khả năng đúng 4/4"""
        from itertools import combinations

        # Tạo tất cả các tổ hợp 4 số từ 6 số
        all_combinations = list(combinations(six_numbers, 4))

        print(f"\n🎲 SMART 4-NUMBER COMBINATIONS:")
        print(f"   From 6 numbers: {six_numbers}")
        print(f"   Total possible combinations: {len(all_combinations)}")

        # Tính điểm ưu tiên cho mỗi bộ
        scored_combinations = []

        for combo in all_combinations:
            score = self.calculate_combination_score(combo, hot_numbers, cold_numbers)
            scored_combinations.append((combo, score))

        # Sắp xếp theo điểm giảm dần
        scored_combinations.sort(key=lambda x: x[1], reverse=True)

        # Chọn top 8-10 bộ có điểm cao nhất (thay vì 11 bộ random)
        selected_combinations = [combo for combo, score in scored_combinations[:10]]

        print(f"   Selected {len(selected_combinations)} SMART combinations:")
        for i, (combo, score) in enumerate(scored_combinations[:10], 1):
            print(f"     Bộ {i:2d}: {list(combo)} (score: {score:.2f})")

        return selected_combinations

    def calculate_combination_score(self, combo, hot_numbers, cold_numbers):
        """Tính điểm ưu tiên cho một bộ 4 số"""
        score = 0.0

        # Chia hot/cold thành các nhóm
        top_3_hot = hot_numbers[:3]
        mid_hot = hot_numbers[3:7]
        top_2_cold = cold_numbers[:2]
        mid_cold = cold_numbers[2:6]

        for num in combo:
            # Điểm cao: số cân bằng (không thuộc top hot/cold)
            if num not in hot_numbers and num not in cold_numbers:
                score += 3.0  # Số cân bằng - ưu tiên cao nhất

            # Điểm trung bình: mid hot (4-7)
            elif num in mid_hot:
                score += 2.0  # Vẫn nóng nhưng không quá

            # Điểm thấp: mid cold (3-6)
            elif num in mid_cold:
                score += 1.0  # Bắt đầu lạnh

            # Điểm rất thấp: top 3 hot hoặc top 2 cold
            elif num in top_3_hot or num in top_2_cold:
                score -= 1.0  # Tránh số quá nóng hoặc quá lạnh

        # Bonus: bộ có sự cân bằng (không quá nhiều số cùng loại)
        balance_bonus = self.calculate_balance_bonus(combo, hot_numbers, cold_numbers)
        score += balance_bonus

        return score

    def calculate_balance_bonus(self, combo, hot_numbers, cold_numbers):
        """Tính điểm bonus cho sự cân bằng của bộ số"""
        hot_count = sum(1 for num in combo if num in hot_numbers)
        cold_count = sum(1 for num in combo if num in cold_numbers)
        neutral_count = 4 - hot_count - cold_count

        # Ưu tiên bộ có sự cân bằng: 1-2 hot, 1-2 cold, 1-2 neutral
        if 1 <= hot_count <= 2 and 1 <= cold_count <= 2 and neutral_count >= 1:
            return 1.0  # Bonus cho sự cân bằng
        elif hot_count == 0 or cold_count == 0:
            return -0.5  # Phạt nếu thiên về một phía
        else:
            return 0.0

    def generate_4_number_combinations(self, six_numbers):
        """Wrapper function để tương thích ngược"""
        # Cần hot_numbers và cold_numbers để tính điểm
        # Tạm thời dùng cách cũ nếu không có thông tin hot/cold
        from itertools import combinations
        all_combinations = list(combinations(six_numbers, 4))
        return all_combinations[:10]  # Trả về 10 bộ đầu tiên

    def analyze_combination_results(self, combinations, actual_results):
        """Phân tích kết quả các bộ 4 số"""
        winning_combinations = []
        losing_combinations = []

        actual_set = set(actual_results)

        for i, combo in enumerate(combinations, 1):
            combo_set = set(combo)
            matches = len(combo_set & actual_set)

            if matches >= 2:  # Trúng nếu có ít nhất 2 số đúng
                winning_combinations.append({
                    'combo_num': i,
                    'numbers': list(combo),
                    'matches': matches,
                    'status': 'WIN'
                })
            else:
                losing_combinations.append({
                    'combo_num': i,
                    'numbers': list(combo),
                    'matches': matches,
                    'status': 'LOSE'
                })

        return winning_combinations, losing_combinations

    def predict(self, day_results=None):
        """Main prediction function - trả về 5 số và 1 bộ 4 số tối ưu - FOCUS ON 4/4 ACCURACY"""
        print("\n" + "="*50)
        print("🎯 KENO PREDICTION - 5 NUMBERS + 1 OPTIMAL 4-NUMBER COMBINATION")
        print("="*50)

        # Sử dụng statistical approach thay vì LSTM
        return self.predict_statistical(day_results)

    def predict_5_numbers_only(self, day_results=None):
        """Chỉ dự đoán 5 số (để tương thích với test functions)"""
        five_numbers, _ = self.predict(day_results)
        return five_numbers

    def test_combinations_for_period(self, day_results, actual_results):
        """Test bộ 4 số cho một kì cụ thể - FOCUS ON 4/4 ACCURACY"""
        # Dự đoán 5 số và tạo 1 bộ 4 số tối ưu
        five_numbers, combinations = self.predict(day_results)

        if not combinations:
            return None

        # Phân tích kết quả cho bộ 4 số duy nhất
        actual_set = set(actual_results)
        best_combo = combinations[0]  # Chỉ có 1 bộ
        combo_set = set(best_combo)
        matches = len(combo_set & actual_set)

        # Đánh giá kết quả
        if matches == 4:
            result_status = "PERFECT_4/4"
        elif matches == 3:
            result_status = "GOOD_3/4"
        elif matches == 2:
            result_status = "OK_2/4"
        else:
            result_status = "POOR_≤1/4"

        return {
            'five_numbers': five_numbers,
            'best_combination': best_combo,
            'matches': matches,
            'result_status': result_status,
            'is_perfect': matches == 4,
            'is_good': matches >= 3
        }

    def get_test_dates(self):
        """Lấy danh sách ngày cần test (từ 2025-04-01)"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-04-01'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            return [row['date'] for row in rows]

        except Exception as e:
            print(f"❌ Error getting test dates: {e}")
            return []

    def get_all_test_dates_from_jan(self):
        """Lấy tất cả ngày từ 2025-01-04 đến nay"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT DISTINCT date
                FROM histories_keno
                WHERE date >= '2025-01-04'
                ORDER BY date ASC
            """

            cursor.execute(query)
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            return [row['date'] for row in rows]

        except Exception as e:
            print(f"❌ Error getting test dates from Jan: {e}")
            return []

    def get_day_draws(self, test_date):
        """Lấy tất cả kì của một ngày"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (test_date,))
            rows = cursor.fetchall()

            cursor.close()
            conn.close()

            # Chuyển đổi results thành list
            for row in rows:
                row['results'] = [int(n) for n in row['results'].split(',')]

            return rows

        except Exception as e:
            print(f"❌ Error getting day draws: {e}")
            return []

    def calculate_accuracy(self, predicted_missing, actual_results):
        """Tính độ chính xác dự đoán số trượt"""
        if not predicted_missing or not actual_results:
            return 0, 0, 0, [], []

        # Số trượt thực tế = các số từ 1-80 không có trong actual_results
        actual_missing = set(range(1, 81)) - set(actual_results)
        predicted_set = set(predicted_missing)

        # Số dự đoán đúng và sai
        correct_numbers = list(predicted_set & actual_missing)  # Số dự đoán đúng
        wrong_numbers = list(predicted_set - actual_missing)    # Số dự đoán sai

        # Số dự đoán đúng
        correct_predictions = len(correct_numbers)

        # Độ chính xác = số dự đoán đúng / tổng số dự đoán
        accuracy = (correct_predictions / len(predicted_missing)) * 100

        return accuracy, correct_predictions, len(predicted_missing), correct_numbers, wrong_numbers

    def analyze_hot_cold_distribution(self, numbers, hot_numbers, cold_numbers, label="Numbers"):
        """Phân tích phân bố hot/cold của một danh sách số"""
        if not numbers:
            return {}

        # Chia hot/cold thành các nhóm
        top_3_hot = hot_numbers[:3]
        mid_hot = hot_numbers[3:7]  # 4-7
        low_hot = hot_numbers[7:10]  # 8-10

        top_2_cold = cold_numbers[:2]
        mid_cold = cold_numbers[2:6]  # 3-6
        low_cold = cold_numbers[6:10]  # 7-10

        # Phân loại từng số
        analysis = {
            'top_3_hot': [],
            'mid_hot': [],
            'low_hot': [],
            'top_2_cold': [],
            'mid_cold': [],
            'low_cold': [],
            'neutral': []
        }

        for num in numbers:
            if num in top_3_hot:
                analysis['top_3_hot'].append(num)
            elif num in mid_hot:
                analysis['mid_hot'].append(num)
            elif num in low_hot:
                analysis['low_hot'].append(num)
            elif num in top_2_cold:
                analysis['top_2_cold'].append(num)
            elif num in mid_cold:
                analysis['mid_cold'].append(num)
            elif num in low_cold:
                analysis['low_cold'].append(num)
            else:
                analysis['neutral'].append(num)

        return analysis

    def log_detailed_analysis(self, predicted_missing, actual_results, hot_numbers, cold_numbers, draw_number, actual_time):
        """Log phân tích chi tiết về hot/cold distribution"""

        # Số trượt thực tế
        actual_missing = list(set(range(1, 81)) - set(actual_results))

        # Phân tích số dự đoán
        pred_analysis = self.analyze_hot_cold_distribution(predicted_missing, hot_numbers, cold_numbers, "Predicted")

        # Phân tích số trượt thực tế
        actual_analysis = self.analyze_hot_cold_distribution(actual_missing, hot_numbers, cold_numbers, "Actual Missing")

        # Phân tích số ra thực tế
        actual_analysis_drawn = self.analyze_hot_cold_distribution(actual_results, hot_numbers, cold_numbers, "Actual Drawn")

        print(f"  📊 DETAILED ANALYSIS for period {draw_number} ({actual_time}):")
        print(f"     🎯 Predicted missing: {predicted_missing}")
        print(f"     ❌ Actual missing: {len(actual_missing)} numbers")
        print(f"     ✅ Actual drawn: {actual_results}")

        print(f"  📈 PREDICTED DISTRIBUTION:")
        if pred_analysis['top_3_hot']:
            print(f"     🚫 Top 3 hot: {pred_analysis['top_3_hot']} (AVOIDED)")
        if pred_analysis['mid_hot']:
            print(f"     ⭐ Mid hot (4-7): {pred_analysis['mid_hot']} (PREFERRED)")
        if pred_analysis['low_hot']:
            print(f"     🔹 Low hot (8-10): {pred_analysis['low_hot']}")
        if pred_analysis['top_2_cold']:
            print(f"     🚫 Top 2 cold: {pred_analysis['top_2_cold']} (AVOIDED)")
        if pred_analysis['mid_cold']:
            print(f"     🔸 Mid cold (3-6): {pred_analysis['mid_cold']}")
        if pred_analysis['low_cold']:
            print(f"     ➖ Low cold (7-10): {pred_analysis['low_cold']}")
        if pred_analysis['neutral']:
            print(f"     ⚪ Neutral: {pred_analysis['neutral']}")

        print(f"  🎲 ACTUAL DRAWN DISTRIBUTION:")
        if actual_analysis_drawn['top_3_hot']:
            print(f"     🔥 Top 3 hot drawn: {actual_analysis_drawn['top_3_hot']}")
        if actual_analysis_drawn['mid_hot']:
            print(f"     🔥 Mid hot drawn: {actual_analysis_drawn['mid_hot']}")
        if actual_analysis_drawn['low_hot']:
            print(f"     🔥 Low hot drawn: {actual_analysis_drawn['low_hot']}")
        if actual_analysis_drawn['top_2_cold']:
            print(f"     🧊 Top 2 cold drawn: {actual_analysis_drawn['top_2_cold']}")
        if actual_analysis_drawn['mid_cold']:
            print(f"     🧊 Mid cold drawn: {actual_analysis_drawn['mid_cold']}")
        if actual_analysis_drawn['low_cold']:
            print(f"     🧊 Low cold drawn: {actual_analysis_drawn['low_cold']}")
        if actual_analysis_drawn['neutral']:
            print(f"     ⚪ Neutral drawn: {actual_analysis_drawn['neutral']}")

        # Tính hiệu quả chiến lược
        strategy_effectiveness = {
            'avoided_top3_hot_correctly': len([n for n in hot_numbers[:3] if n not in actual_results]),
            'avoided_top2_cold_correctly': len([n for n in cold_numbers[:2] if n not in actual_results]),
            'preferred_mid_hot_success': len([n for n in pred_analysis['mid_hot'] if n not in actual_results]),
            'total_avoided': len(hot_numbers[:3]) + len(cold_numbers[:2]),
            'total_preferred': len(pred_analysis['mid_hot'])
        }

        print(f"  🎯 STRATEGY EFFECTIVENESS:")
        print(f"     Avoided top 3 hot correctly: {strategy_effectiveness['avoided_top3_hot_correctly']}/3")
        print(f"     Avoided top 2 cold correctly: {strategy_effectiveness['avoided_top2_cold_correctly']}/2")
        if strategy_effectiveness['total_preferred'] > 0:
            print(f"     Preferred mid hot success: {strategy_effectiveness['preferred_mid_hot_success']}/{strategy_effectiveness['total_preferred']}")

        return strategy_effectiveness

    def test_single_day(self, test_date):
        """Test dự đoán cho một ngày từ kì 51 - FOCUS ON 4/4 ACCURACY"""
        print(f"\n=== Test ngày {test_date} - FOCUS ON 4/4 ACCURACY ===")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        predictions_count = 0
        perfect_4_4_count = 0    # Số lần đúng 4/4
        good_3_4_count = 0       # Số lần đúng 3/4
        ok_2_4_count = 0         # Số lần đúng 2/4
        poor_count = 0           # Số lần đúng ≤1/4

        # Test từ kì 51 đến cuối ngày
        for draw_index in range(50, len(day_draws)):  # Từ kì 51 (index 50)
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 5 số và 1 bộ 4 số tối ưu (tắt logging chi tiết trong test)
            try:
                # Tạm thời tắt print để test nhanh hơn
                import sys
                from io import StringIO
                old_stdout = sys.stdout
                sys.stdout = StringIO()

                # Test bộ 4 số
                result = self.test_combinations_for_period(input_draws, day_draws[draw_index]['results'])

                # Khôi phục stdout
                sys.stdout = old_stdout

                if result:
                    actual_time = day_draws[draw_index]['time']
                    matches = result['matches']
                    status = result['result_status']

                    predictions_count += 1

                    # Đếm các loại kết quả
                    if matches == 4:
                        perfect_4_4_count += 1
                        status_icon = "🎯"  # Perfect 4/4
                    elif matches == 3:
                        good_3_4_count += 1
                        status_icon = "🔥"  # Good 3/4
                    elif matches == 2:
                        ok_2_4_count += 1
                        status_icon = "⚡"  # OK 2/4
                    else:
                        poor_count += 1
                        status_icon = "❌"  # Poor ≤1/4

                    print(f"Kì {draw_number:3d} ({actual_time}): {matches}/4 đúng - {status} {status_icon}")

                    # Log chi tiết cho kết quả Perfect
                    if matches == 4:
                        print(f"   🎯 PERFECT 4/4: {result['best_combination']} vs {day_draws[draw_index]['results']}")

            except Exception as e:
                print(f"❌ Error predicting period {draw_number}: {e}")

        # Tính độ chính xác cho cả ngày
        if predictions_count > 0:
            perfect_4_4_rate = (perfect_4_4_count / predictions_count) * 100
            good_3_4_rate = (good_3_4_count / predictions_count) * 100
            ok_2_4_rate = (ok_2_4_count / predictions_count) * 100
            poor_rate = (poor_count / predictions_count) * 100

            print(f"📊 {test_date}: 4/4 Perfect: {perfect_4_4_count}/{predictions_count} = {perfect_4_4_rate:.1f}%")
            print(f"   3/4 Good: {good_3_4_count} = {good_3_4_rate:.1f}%, 2/4 OK: {ok_2_4_count} = {ok_2_4_rate:.1f}%")

            result = {
                'date': str(test_date),
                'predictions_count': predictions_count,
                'perfect_4_4_count': perfect_4_4_count,
                'perfect_4_4_rate': perfect_4_4_rate,
                'good_3_4_count': good_3_4_count,
                'good_3_4_rate': good_3_4_rate,
                'ok_2_4_count': ok_2_4_count,
                'ok_2_4_rate': ok_2_4_rate,
                'poor_count': poor_count,
                'poor_rate': poor_rate
            }

            return result

        return None

    def test_new_system(self, test_date=None):
        """Test hệ thống mới với 5 số và 1 bộ 4 số tối ưu"""
        if not test_date:
            # Lấy ngày gần nhất
            test_dates = self.get_test_dates()
            if test_dates:
                test_date = test_dates[-1]
            else:
                print("❌ No test dates available")
                return

        print(f"\n🎯 TESTING NEW SYSTEM - 5 NUMBERS + 1 OPTIMAL 4-COMBO")
        print(f"📅 Test date: {test_date}")
        print("="*60)

        # Test một ngày
        result = self.test_single_day(test_date)

        if result:
            print(f"\n📊 SUMMARY FOR {test_date}:")
            print(f"   Total predictions: {result['predictions_count']}")
            print(f"   🎯 Perfect 4/4: {result['perfect_4_4_count']} ({result['perfect_4_4_rate']:.1f}%)")
            print(f"   🔥 Good 3/4: {result['good_3_4_count']} ({result['good_3_4_rate']:.1f}%)")
            print(f"   ⚡ OK 2/4: {result['ok_2_4_count']} ({result['ok_2_4_rate']:.1f}%)")
            print(f"   ❌ Poor ≤1/4: {result['poor_count']} ({result['poor_rate']:.1f}%)")

            # Tính tỷ lệ thành công (≥3/4)
            success_count = result['perfect_4_4_count'] + result['good_3_4_count']
            success_rate = (success_count / result['predictions_count']) * 100
            print(f"\n🏆 SUCCESS RATE (≥3/4): {success_count}/{result['predictions_count']} = {success_rate:.1f}%")
        else:
            print("❌ No results for this date")

    def test_single_day_with_detailed_logging(self, test_date):
        """Test dự đoán cho một ngày với detailed logging (không tắt stdout)"""
        print(f"\n=== Test chi tiết ngày {test_date} ===")

        # Lấy tất cả kì của ngày
        day_draws = self.get_day_draws(test_date)

        if len(day_draws) < 51:
            print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, bỏ qua")
            return None

        predictions_count = 0
        perfect_predictions = 0  # Số lần dự đoán đúng ≥4/5 số trượt
        good_predictions = 0     # Số lần dự đoán đúng 5/5 số trượt
        normal_predictions = 0   # Số lần dự đoán đúng 4/5 số trượt
        bad_predictions = 0      # Số lần dự đoán đúng ≤3/5 số trượt

        # Thống kê chiến lược tổng hợp
        total_strategy_stats = {
            'avoided_top3_hot_correctly': 0,
            'avoided_top2_cold_correctly': 0,
            'preferred_mid_hot_success': 0,
            'total_avoided_attempts': 0,
            'total_preferred_attempts': 0
        }

        # Test từ kì 51 đến cuối ngày (hoặc chỉ test 10 kì đầu để demo)
        max_test_periods = min(len(day_draws), 60)  # Test tối đa 10 kì để không quá dài

        for draw_index in range(50, max_test_periods):  # Từ kì 51
            draw_number = draw_index + 1

            # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
            input_draws = []
            for i in range(draw_index):
                input_draws.append(day_draws[i]['results'])

            # Dự đoán 5 số trượt với full logging
            try:
                print(f"\n🔄 PREDICTING PERIOD {draw_number}:")
                predicted_missing = self.predict(day_results=input_draws)

                if predicted_missing:
                    # Lấy kết quả thực tế
                    actual_results = day_draws[draw_index]['results']
                    actual_time = day_draws[draw_index]['time']

                    # Tính độ chính xác
                    _, correct, _, _, _ = self.calculate_accuracy(predicted_missing, actual_results)

                    predictions_count += 1

                    # Đếm các loại dự đoán
                    if correct >= 5:
                        good_predictions += 1  # 5/5 = Good
                        perfect_predictions += 1
                    elif correct >= 4:
                        normal_predictions += 1  # 4/5 = Normal
                        perfect_predictions += 1
                    else:
                        bad_predictions += 1  # ≤3/5 = Bad

                    # Hiển thị kết quả với icon khác nhau
                    if correct >= 5:
                        label = "Good"
                        status_icon = "⭐"  # 5/5 đúng
                    elif correct >= 4:
                        label = "Normal"
                        status_icon = "🔥"  # 4/5 đúng - icon khác
                    elif correct <= 2:
                        label = "Bad"
                        status_icon = "❌"  # ≤2/5 đúng
                    else:
                        label = "Bad"
                        status_icon = "⚠️"  # 3/5 đúng

                    print(f"\n✅ RESULT: Kì {draw_number:3d} ({actual_time}): {correct}/5 đúng - {label} {status_icon}")

                    # Log chi tiết hot/cold distribution cho tất cả kết quả
                    # Lấy hot/cold numbers từ 50 kì gần nhất
                    recent_50 = input_draws[-50:] if len(input_draws) >= 50 else input_draws
                    hot_numbers, cold_numbers = self.get_hot_cold_numbers(recent_50)

                    strategy_effectiveness = self.log_detailed_analysis(
                        predicted_missing, actual_results, hot_numbers, cold_numbers, draw_number, actual_time
                    )

                    # Cập nhật thống kê tổng hợp
                    total_strategy_stats['avoided_top3_hot_correctly'] += strategy_effectiveness['avoided_top3_hot_correctly']
                    total_strategy_stats['avoided_top2_cold_correctly'] += strategy_effectiveness['avoided_top2_cold_correctly']
                    total_strategy_stats['preferred_mid_hot_success'] += strategy_effectiveness['preferred_mid_hot_success']
                    total_strategy_stats['total_avoided_attempts'] += strategy_effectiveness['total_avoided']
                    total_strategy_stats['total_preferred_attempts'] += strategy_effectiveness['total_preferred']

                    print("-" * 80)

            except Exception as e:
                print(f"❌ Error predicting period {draw_number}: {e}")

        # Tính độ chính xác cho cả ngày
        if predictions_count > 0:
            perfect_rate = (perfect_predictions / predictions_count) * 100
            good_rate = (good_predictions / predictions_count) * 100
            normal_rate = (normal_predictions / predictions_count) * 100
            bad_rate = (bad_predictions / predictions_count) * 100

            print(f"\n📊 DAILY SUMMARY FOR {test_date}:")
            print(f"   Perfect(≥4/5): {perfect_predictions}/{predictions_count} = {perfect_rate:.1f}%")
            print(f"   Good(5/5): {good_predictions}/{predictions_count} = {good_rate:.1f}%")
            print(f"   Normal(4/5): {normal_predictions}/{predictions_count} = {normal_rate:.1f}%")
            print(f"   Bad(≤3/5): {bad_predictions}/{predictions_count} = {bad_rate:.1f}%")

            # Thống kê chiến lược tổng hợp
            print(f"\n🎯 STRATEGY EFFECTIVENESS SUMMARY:")
            if total_strategy_stats['total_avoided_attempts'] > 0:
                avoid_success_rate = ((total_strategy_stats['avoided_top3_hot_correctly'] +
                                     total_strategy_stats['avoided_top2_cold_correctly']) /
                                    total_strategy_stats['total_avoided_attempts']) * 100
                print(f"   Avoid strategy success: {total_strategy_stats['avoided_top3_hot_correctly'] + total_strategy_stats['avoided_top2_cold_correctly']}/{total_strategy_stats['total_avoided_attempts']} = {avoid_success_rate:.1f}%")

            if total_strategy_stats['total_preferred_attempts'] > 0:
                prefer_success_rate = (total_strategy_stats['preferred_mid_hot_success'] /
                                     total_strategy_stats['total_preferred_attempts']) * 100
                print(f"   Prefer strategy success: {total_strategy_stats['preferred_mid_hot_success']}/{total_strategy_stats['total_preferred_attempts']} = {prefer_success_rate:.1f}%")

            result = {
                'date': str(test_date),
                'predictions_count': predictions_count,
                'perfect_predictions': perfect_predictions,
                'perfect_rate': perfect_rate,
                'good_predictions': good_predictions,
                'good_rate': good_rate,
                'normal_predictions': normal_predictions,
                'normal_rate': normal_rate,
                'bad_predictions': bad_predictions,
                'bad_rate': bad_rate,
                'strategy_stats': total_strategy_stats
            }

            return result

        return None

    def test_all_days(self):
        """Test tất cả ngày từ 2025-04-01"""
        print("=== Test độ chính xác dự đoán (từ 2025-04-01) ===")
        print("Sử dụng: Simplified Keno Predictor")
        print(f"- LSTM Model + Loại bỏ số nóng (>{self.exclusion_threshold} lần/{self.exclusion_window} kì)")
        print(f"- Top {self.cold_number_count} số lạnh")

        test_dates = self.get_test_dates()

        if not test_dates:
            print("Không có ngày nào để test")
            return

        print(f"Sẽ test {len(test_dates)} ngày")

        total_perfect_predictions = 0
        total_prediction_count = 0
        successful_days = 0
        daily_results = []

        for i, test_date in enumerate(test_dates, 1):
            print(f"\n[{i}/{len(test_dates)}]", end=" ")

            day_result = self.test_single_day(test_date)

            if day_result:
                total_perfect_predictions += day_result['perfect_predictions']
                total_prediction_count += day_result['predictions_count']
                successful_days += 1
                daily_results.append(day_result)

        # Tổng kết
        if total_prediction_count > 0:
            overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100

            print(f"\n=== TỔNG KẾT ===")
            print(f"Dự đoán xuất sắc (≥4/5): {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.2f}%)")
            print(f"Số ngày test: {successful_days}/{len(test_dates)}")
            print(f"Phương pháp: Simplified Predictor (LSTM + Exclusion + Cold Numbers)")

            # Thống kê chi tiết
            self.print_daily_summary(daily_results, total_perfect_predictions, total_prediction_count)
        else:
            print("\nKhông có kết quả test nào")

    def print_daily_summary(self, daily_results, total_perfect_predictions, total_prediction_count):
        """In thống kê chi tiết các ngày"""
        print(f"\n" + "="*70)
        print("📊 THỐNG KÊ CHI TIẾT CÁC NGÀY")
        print("="*70)

        if not daily_results:
            print("Không có dữ liệu ngày để thống kê")
            return

        # Sắp xếp theo ngày
        sorted_results = sorted(daily_results, key=lambda x: x['date'])

        # Header bảng
        print(f"{'Ngày':<12} {'Good':<8} {'Normal':<8} {'≥4/5':<8} {'Bad':<8} {'Kì':<5}")
        print("-" * 60)

        # Tính tổng
        total_good = sum(r['good_predictions'] for r in sorted_results)
        total_normal = sum(r['normal_predictions'] for r in sorted_results)
        total_bad = sum(r['bad_predictions'] for r in sorted_results)

        # In từng ngày
        for result in sorted_results:
            date_str = str(result['date'])
            good_str = f"{result['good_predictions']}({result['good_rate']:.0f}%)"
            normal_str = f"{result['normal_predictions']}({result['normal_rate']:.0f}%)"
            perfect_str = f"{result['perfect_predictions']}({result['perfect_rate']:.0f}%)"
            bad_str = f"{result['bad_predictions']}({result['bad_rate']:.0f}%)"
            predictions_str = f"{result['predictions_count']}"

            print(f"{date_str:<12} {good_str:<8} {normal_str:<8} {perfect_str:<8} {bad_str:<8} {predictions_str:<5}")

        # Thống kê tổng hợp
        print("-" * 60)
        overall_perfect_rate = (total_perfect_predictions / total_prediction_count) * 100
        overall_good_rate = (total_good / total_prediction_count) * 100
        overall_normal_rate = (total_normal / total_prediction_count) * 100
        overall_bad_rate = (total_bad / total_prediction_count) * 100

        good_total_str = f"{total_good}({overall_good_rate:.0f}%)"
        normal_total_str = f"{total_normal}({overall_normal_rate:.0f}%)"
        perfect_total_str = f"{total_perfect_predictions}({overall_perfect_rate:.0f}%)"
        bad_total_str = f"{total_bad}({overall_bad_rate:.0f}%)"

        print(f"{'TỔNG':<12} {good_total_str:<8} {normal_total_str:<8} {perfect_total_str:<8} {bad_total_str:<8} {total_prediction_count:<5}")

        print(f"\n📈 PHÂN TÍCH CHI TIẾT:")
        print(f"   • 5/5 đúng (Good):      {total_good}/{total_prediction_count} lần ({overall_good_rate:.1f}%)")
        print(f"   • 4/5 đúng (Normal):    {total_normal}/{total_prediction_count} lần ({overall_normal_rate:.1f}%)")
        print(f"   • ≥4/5 đúng (Perfect):  {total_perfect_predictions}/{total_prediction_count} lần ({overall_perfect_rate:.1f}%)")
        print(f"   • ≤3/5 đúng (Bad):      {total_bad}/{total_prediction_count} lần ({overall_bad_rate:.1f}%)")

        print("="*70)

def predict_5_numbers_and_best_4():
    """Dự đoán 5 số và chọn 1 bộ 4 số tốt nhất - FOCUS ON 4/4 ACCURACY"""
    predictor = SimplifiedKenoPredictor()
    return predictor.predict_statistical()

def predict_5_numbers_only():
    """Dự đoán nhanh 5 số với Statistical approach"""
    predictor = SimplifiedKenoPredictor()
    five_numbers, _ = predictor.predict_statistical()
    return five_numbers

def predict_statistical_demo():
    """Demo Statistical prediction với consecutive exclusion"""
    predictor = SimplifiedKenoPredictor()
    return predictor.predict_statistical()

def test_optimized_4_accuracy():
    """Test hệ thống tối ưu cho 4/4 accuracy với consecutive exclusion"""
    print("🧪 TESTING OPTIMIZED 4/4 ACCURACY SYSTEM")
    print("="*70)

    predictor = SimplifiedKenoPredictor()

    # Test với dữ liệu demo
    demo_data = [
        [4, 23, 45, 67, 89, 90, 2, 3, 51, 6, 7, 8, 9, 10, 11, 13, 14, 27, 16, 17],
        [1, 5, 12, 34, 56, 78, 9, 11, 22, 33, 44, 55, 66, 77, 88, 99, 10, 20, 30, 40],
        [15, 25, 35, 45, 55, 65, 75, 85, 95, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 60]
    ]

    # Mở rộng demo data để test consecutive exclusion (cần ít nhất 50 kì)
    extended_data = []
    for i in range(60):  # Tạo 60 kì để đủ dữ liệu
        # Tạo data có một số xuất hiện liên tục
        base_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]

        # Thêm random numbers để đủ 20 số mỗi kì
        import random
        random.seed(i)  # Để kết quả reproducible
        additional_numbers = random.sample(range(21, 81), 0)  # Không thêm số nào
        base_numbers.extend(additional_numbers)

        # Số 77 xuất hiện liên tục trong 4 kì đầu
        if i < 4:
            base_numbers.append(77)

        # Số 55 xuất hiện liên tục trong kì 10-12 (3 kì)
        if 10 <= i < 13:
            base_numbers.append(55)

        extended_data.append(base_numbers)

    print(f"📊 Demo data: {len(extended_data)} periods")
    print(f"   Number 77 appears in periods 1-4 consecutively (should be excluded)")
    print(f"   Number 55 appears in periods 11-13 consecutively (should be excluded)")

    # Test prediction
    five_numbers, combinations = predictor.predict_statistical(day_results=extended_data)

    if five_numbers and combinations:
        print(f"\n✅ PREDICTION RESULTS:")
        print(f"   5 candidates: {five_numbers}")
        print(f"   Best 4-combo: {combinations[0]}")
        print(f"\n🔍 CONSECUTIVE EXCLUSION TEST:")
        print(f"   Should exclude 77: {'✅' if 77 not in five_numbers else '❌'}")
        print(f"   Should exclude 55: {'✅' if 55 not in five_numbers else '❌'}")

        # Test recovery mechanism
        print(f"\n🔄 RECOVERY MECHANISM:")
        excluded_count = len([n for n in [77, 55] if n not in five_numbers])
        print(f"   Successfully excluded {excluded_count}/2 consecutive numbers")

        if excluded_count == 2:
            print(f"   🎯 PERFECT! Consecutive exclusion working correctly")
        elif excluded_count == 1:
            print(f"   ⚠️ PARTIAL! One number still included")
        else:
            print(f"   ❌ FAILED! Consecutive exclusion not working")
    else:
        print("\n❌ Prediction failed")

    return five_numbers, combinations

def test_optimized_system_comprehensive():
    """Test hệ thống tối ưu với tất cả dữ liệu từ 2025-01-04"""
    print("🧪 COMPREHENSIVE TEST - OPTIMIZED 4/4 ACCURACY SYSTEM")
    print("="*80)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả ngày từ 2025-01-04
    test_dates = predictor.get_all_test_dates_from_jan()

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return None

    print(f"📅 Sẽ test {len(test_dates)} ngày từ {test_dates[0]} đến {test_dates[-1]}")
    print(f"🎯 FOCUS: Chỉ quan tâm 4/4 đúng với consecutive exclusion")
    print("="*80)

    # Thống kê tổng hợp
    total_stats = {
        'total_predictions': 0,
        'total_4_correct': 0,
        'total_3_correct': 0,
        'total_2_correct': 0,
        'total_1_correct': 0,
        'total_0_correct': 0,
        'days_tested': 0,
        'periods_tested': 0,
        'consecutive_exclusions': 0
    }

    daily_results = []

    # Test từng ngày (giới hạn 10 kì/ngày)
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n[{i}/{len(test_dates)}] Testing {test_date}...")

        day_result = test_single_day_optimized(test_date, predictor, max_periods=10)

        if day_result:
            daily_results.append(day_result)
            total_stats['days_tested'] += 1
            total_stats['periods_tested'] += day_result['periods_tested']
            total_stats['total_predictions'] += day_result['total_predictions']
            total_stats['total_4_correct'] += day_result['4_correct']
            total_stats['total_3_correct'] += day_result['3_correct']
            total_stats['total_2_correct'] += day_result['2_correct']
            total_stats['total_1_correct'] += day_result['1_correct']
            total_stats['total_0_correct'] += day_result['0_correct']
            total_stats['consecutive_exclusions'] += day_result.get('exclusions', 0)

            # In kết quả ngắn gọn
            rate_4_correct = (day_result['4_correct'] / day_result['total_predictions']) * 100 if day_result['total_predictions'] > 0 else 0
            print(f"   {test_date}: {day_result['periods_tested']} kì, {day_result['total_predictions']} predictions")
            print(f"     4/4 đúng: {day_result['4_correct']} ({rate_4_correct:.1f}%)")
            if day_result.get('exclusions', 0) > 0:
                print(f"     Exclusions: {day_result['exclusions']}")

    # Phân tích tổng hợp
    if total_stats['total_predictions'] > 0:
        analyze_optimized_results(total_stats, daily_results)

    return total_stats

def test_single_day_optimized(test_date, predictor, max_periods=10):
    """Test hệ thống tối ưu cho một ngày"""
    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        return None

    # Giới hạn số kì test
    max_test = min(50 + max_periods, len(day_draws))

    day_stats = {
        'date': test_date,
        'periods_tested': 0,
        'total_predictions': 0,
        '4_correct': 0,
        '3_correct': 0,
        '2_correct': 0,
        '1_correct': 0,
        '0_correct': 0,
        'exclusions': 0
    }

    # Test từ kì 51
    for draw_index in range(50, max_test):
        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán với hệ thống tối ưu (tắt logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            five_numbers, combinations = predictor.predict_statistical(day_results=input_draws)

            sys.stdout = old_stdout

            if five_numbers and combinations:
                actual_results = day_draws[draw_index]['results']

                # Lấy bộ 4 số dự đoán
                predicted_4 = combinations[0]

                # Tính số trượt thực tế
                actual_missing = set(range(1, 81)) - set(actual_results)

                # Tính số dự đoán đúng
                correct_count = len(set(predicted_4) & actual_missing)

                day_stats['periods_tested'] += 1
                day_stats['total_predictions'] += 1

                # Phân loại kết quả
                if correct_count == 4:
                    day_stats['4_correct'] += 1
                elif correct_count == 3:
                    day_stats['3_correct'] += 1
                elif correct_count == 2:
                    day_stats['2_correct'] += 1
                elif correct_count == 1:
                    day_stats['1_correct'] += 1
                else:
                    day_stats['0_correct'] += 1

                # Đếm exclusions (nếu có số bị loại bỏ)
                if hasattr(predictor, 'consecutive_tracker') and predictor.consecutive_tracker:
                    day_stats['exclusions'] += len(predictor.consecutive_tracker)

        except Exception:
            continue

    return day_stats if day_stats['periods_tested'] > 0 else None

def analyze_optimized_results(total_stats, daily_results):
    """Phân tích kết quả hệ thống tối ưu"""
    print(f"\n" + "="*80)
    print("📊 OPTIMIZED SYSTEM COMPREHENSIVE RESULTS")
    print("="*80)

    # Thống kê tổng quan
    total_predictions = total_stats['total_predictions']
    rate_4_correct = (total_stats['total_4_correct'] / total_predictions) * 100
    rate_3_correct = (total_stats['total_3_correct'] / total_predictions) * 100
    rate_2_correct = (total_stats['total_2_correct'] / total_predictions) * 100

    print(f"🎯 TỔNG QUAN OPTIMIZED SYSTEM:")
    print(f"   Số ngày test: {total_stats['days_tested']}")
    print(f"   Số kì test: {total_stats['periods_tested']}")
    print(f"   Tổng predictions: {total_predictions}")
    print(f"   4/4 đúng: {total_stats['total_4_correct']} ({rate_4_correct:.2f}%)")
    print(f"   3/4 đúng: {total_stats['total_3_correct']} ({rate_3_correct:.1f}%)")
    print(f"   2/4 đúng: {total_stats['total_2_correct']} ({rate_2_correct:.1f}%)")
    print(f"   Consecutive exclusions: {total_stats['consecutive_exclusions']}")

    # So sánh với statistical approach cũ
    print(f"\n📈 SO SÁNH VỚI STATISTICAL APPROACH CŨ:")
    print(f"   Statistical cũ 4/4 rate: 31.72% (3115/9820)")
    print(f"   Optimized mới 4/4 rate: {rate_4_correct:.2f}% ({total_stats['total_4_correct']}/{total_predictions})")

    if rate_4_correct > 31.72:
        improvement = rate_4_correct / 31.72
        print(f"   🔥 IMPROVEMENT: {improvement:.2f}x better than old statistical!")
    elif rate_4_correct > 25.0:
        print(f"   ✅ EXCELLENT: Still very high accuracy!")
    else:
        print(f"   ⚠️ Need optimization")

    # Top 10 ngày tốt nhất
    print(f"\n🏆 TOP 10 NGÀY TỐT NHẤT CHO 4/4 ĐÚNG:")
    sorted_days = []
    for day in daily_results:
        if day['total_predictions'] > 0:
            rate_4_correct_day = (day['4_correct'] / day['total_predictions']) * 100
            sorted_days.append({
                'date': day['date'],
                '4_correct': day['4_correct'],
                'total_predictions': day['total_predictions'],
                'rate_4_correct': rate_4_correct_day
            })

    sorted_days.sort(key=lambda x: x['rate_4_correct'], reverse=True)

    for i, day in enumerate(sorted_days[:10], 1):
        print(f"   {i:2d}. {day['date']}: {day['rate_4_correct']:.1f}% "
              f"({day['4_correct']}/{day['total_predictions']} predictions)")

    # Kết luận
    print(f"\n💡 KẾT LUẬN OPTIMIZED SYSTEM:")

    if rate_4_correct >= 35.0:
        print(f"   🚀 BREAKTHROUGH! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% vượt xa mong đợi!")
    elif rate_4_correct >= 30.0:
        print(f"   ✅ EXCELLENT! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% rất cao!")
    elif rate_4_correct >= 25.0:
        print(f"   🔥 VERY GOOD! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% tốt!")
    else:
        print(f"   ⚠️ Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% cần cải thiện thêm")

    print("="*80)

def test_optimized_detailed_profit_focus():
    """Test chi tiết hệ thống tối ưu - CHỈ QUAN TÂM 4/4 ĐÚNG = LỢI NHUẬN"""
    print("🧪 DETAILED PROFIT-FOCUSED TEST - OPTIMIZED SYSTEM")
    print("="*100)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả ngày từ 2025-01-04
    test_dates = predictor.get_all_test_dates_from_jan()

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return None

    print(f"📅 Sẽ test {len(test_dates)} ngày từ {test_dates[0]} đến {test_dates[-1]}")
    print(f"💰 FOCUS: CHỈ 4/4 đúng = LỢI NHUẬN, ≤3/4 = THUA LỖ")
    print(f"📝 Format: [Ngày] Kì XX (HH:MM) | Dự đoán: [a,b,c,d] | Trượt thực tế: [x,y,z,...] | Kết quả: X/4")
    print("="*100)

    # Thống kê tổng hợp
    total_stats = {
        'total_predictions': 0,
        'total_wins': 0,  # 4/4 đúng = thắng
        'total_losses': 0,  # ≤3/4 = thua
        'days_tested': 0,
        'periods_tested': 0,
        'profit_periods': [],  # Lưu các kì có lợi nhuận
        'loss_periods': []     # Lưu các kì thua lỗ
    }

    # Test từng ngày
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n📅 [{i}/{len(test_dates)}] {test_date}")
        print("-" * 80)

        day_result = test_single_day_profit_focus(test_date, predictor, total_stats)

        if day_result:
            total_stats['days_tested'] += 1
            total_stats['periods_tested'] += day_result['periods_tested']
            total_stats['total_predictions'] += day_result['total_predictions']
            total_stats['total_wins'] += day_result['wins']
            total_stats['total_losses'] += day_result['losses']

    # Tổng kết cuối cùng
    print(f"\n" + "="*100)
    print("💰 TỔNG KẾT LỢI NHUẬN")
    print("="*100)

    if total_stats['total_predictions'] > 0:
        win_rate = (total_stats['total_wins'] / total_stats['total_predictions']) * 100
        loss_rate = (total_stats['total_losses'] / total_stats['total_predictions']) * 100

        print(f"🎯 THỐNG KÊ LỢI NHUẬN:")
        print(f"   Số ngày test: {total_stats['days_tested']}")
        print(f"   Số kì test: {total_stats['periods_tested']}")
        print(f"   Tổng predictions: {total_stats['total_predictions']}")
        print(f"   🏆 THẮNG (4/4): {total_stats['total_wins']} ({win_rate:.2f}%)")
        print(f"   💸 THUA (≤3/4): {total_stats['total_losses']} ({loss_rate:.2f}%)")

        # Tính lợi nhuận ước tính
        cost_per_bet = 10000  # 10k/bộ
        win_reward = 500000   # 500k/bộ 4/4

        total_cost = total_stats['total_predictions'] * cost_per_bet
        total_revenue = total_stats['total_wins'] * win_reward
        net_profit = total_revenue - total_cost

        print(f"\n💰 PHÂN TÍCH LỢI NHUẬN (10k/bộ, 500k thưởng 4/4):")
        print(f"   Chi phí: {total_stats['total_predictions']} × 10k = {total_cost:,}k")
        print(f"   Thu nhập: {total_stats['total_wins']} × 500k = {total_revenue:,}k")
        print(f"   Lợi nhuận: {net_profit:,}k")

        if net_profit > 0:
            roi = (net_profit / total_cost) * 100
            print(f"   📈 ROI: {roi:.1f}% - {'🚀 XUẤT SẮC!' if roi > 100 else '✅ TỐT!' if roi > 50 else '⚠️ CHẤP NHẬN ĐƯỢC'}")
        else:
            print(f"   📉 LỖ: {abs(net_profit):,}k - ❌ CẦN CẢI THIỆN")

        # Phân tích ngày tốt nhất
        print(f"\n🏆 PHÂN TÍCH NGÀY CÓ LỢI NHUẬN:")
        profit_days = {}
        for period in total_stats['profit_periods']:
            date = period['date']
            if date not in profit_days:
                profit_days[date] = {'wins': 0, 'total': 0}
            profit_days[date]['wins'] += 1
            profit_days[date]['total'] += 1

        for period in total_stats['loss_periods']:
            date = period['date']
            if date not in profit_days:
                profit_days[date] = {'wins': 0, 'total': 0}
            profit_days[date]['total'] += 1

        # Sắp xếp theo tỷ lệ thắng
        sorted_days = []
        for date, stats in profit_days.items():
            win_rate_day = (stats['wins'] / stats['total']) * 100 if stats['total'] > 0 else 0
            sorted_days.append({
                'date': date,
                'wins': stats['wins'],
                'total': stats['total'],
                'win_rate': win_rate_day
            })

        sorted_days.sort(key=lambda x: x['win_rate'], reverse=True)

        print(f"   Top 10 ngày có tỷ lệ thắng cao nhất:")
        for i, day in enumerate(sorted_days[:10], 1):
            daily_profit = (day['wins'] * win_reward) - (day['total'] * cost_per_bet)
            print(f"   {i:2d}. {day['date']}: {day['win_rate']:.1f}% ({day['wins']}/{day['total']}) - "
                  f"Lãi: {daily_profit:,}k")

    print("="*100)
    return total_stats

def test_single_day_profit_focus(test_date, predictor, total_stats):
    """Test một ngày với focus vào lợi nhuận"""
    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        print(f"   ⚠️ Chỉ có {len(day_draws)} kì, cần ít nhất 51 kì")
        return None

    day_stats = {
        'date': test_date,
        'periods_tested': 0,
        'total_predictions': 0,
        'wins': 0,  # 4/4 đúng
        'losses': 0  # ≤3/4
    }

    # Test từ kì 51 đến cuối ngày
    for draw_index in range(50, len(day_draws)):
        draw_number = draw_index + 1

        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán với hệ thống tối ưu (tắt logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            five_numbers, combinations = predictor.predict_statistical(day_results=input_draws)

            sys.stdout = old_stdout

            if five_numbers and combinations:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Lấy bộ 4 số dự đoán
                predicted_4 = combinations[0]

                # Tính số trượt thực tế
                actual_missing = sorted(list(set(range(1, 81)) - set(actual_results)))

                # Tính số dự đoán đúng
                correct_numbers = set(predicted_4) & set(actual_missing)
                correct_count = len(correct_numbers)

                day_stats['periods_tested'] += 1
                day_stats['total_predictions'] += 1

                # Phân loại: CHỈ 4/4 = THẮNG, còn lại = THUA
                if correct_count == 4:
                    status = "🎯 THẮNG"
                    day_stats['wins'] += 1
                    # Lưu vào danh sách lợi nhuận
                    total_stats['profit_periods'].append({
                        'date': test_date,
                        'period': draw_number,
                        'time': actual_time,
                        'predicted': predicted_4,
                        'correct': sorted(list(correct_numbers))
                    })
                else:
                    status = "💸 THUA"
                    day_stats['losses'] += 1
                    # Lưu vào danh sách thua lỗ
                    total_stats['loss_periods'].append({
                        'date': test_date,
                        'period': draw_number,
                        'time': actual_time,
                        'predicted': predicted_4,
                        'correct': sorted(list(correct_numbers)),
                        'correct_count': correct_count
                    })

                # In kết quả chi tiết
                print(f"   Kì {draw_number:3d} ({actual_time}) | "
                      f"Dự đoán: {predicted_4} | "
                      f"Trượt thực tế: {actual_missing[:10]}{'...' if len(actual_missing) > 10 else ''} | "
                      f"Đúng: {sorted(list(correct_numbers))} | "
                      f"Kết quả: {correct_count}/4 {status}")

        except Exception as e:
            print(f"   ❌ Lỗi kì {draw_number}: {str(e)[:50]}")
            continue

    # Tổng kết ngày
    if day_stats['periods_tested'] > 0:
        win_rate = (day_stats['wins'] / day_stats['total_predictions']) * 100
        daily_profit = (day_stats['wins'] * 500000) - (day_stats['total_predictions'] * 10000)

        print(f"\n   📊 Tổng kết {test_date}: {day_stats['periods_tested']} kì")
        print(f"       🏆 Thắng: {day_stats['wins']}/{day_stats['total_predictions']} ({win_rate:.1f}%)")
        print(f"       💰 Lợi nhuận ước tính: {daily_profit:,}k")

    return day_stats if day_stats['periods_tested'] > 0 else None

def test_strategy_logic():
    """Test logic chiến lược mà không cần LSTM model"""
    print("🧪 TESTING STRATEGY LOGIC")
    print("="*50)

    # Tạo dữ liệu giả để test logic
    fake_lstm_predictions = [1, 5, 12, 23, 34, 45, 56, 67, 78, 80]
    fake_hot_numbers = [23, 45, 67, 12, 34, 56, 78, 1, 5, 80]  # Top 10 hot
    fake_cold_numbers = [15, 25, 35, 40, 50, 60, 70, 75, 77, 79]  # Top 10 cold
    fake_excluded = {23}  # Số 23 bị loại trừ

    print(f"📊 Test data:")
    print(f"   LSTM predictions: {fake_lstm_predictions}")
    print(f"   Hot numbers: {fake_hot_numbers}")
    print(f"   Cold numbers: {fake_cold_numbers}")
    print(f"   Excluded: {fake_excluded}")

    # Tạo predictor và test logic
    predictor = SimplifiedKenoPredictor()

    # Test analyze_matches_and_prioritize
    _, _ = predictor.analyze_matches_and_prioritize(
        fake_lstm_predictions, fake_hot_numbers, fake_cold_numbers
    )

    # Test select_final_5_numbers
    final_5 = predictor.select_final_5_numbers(
        fake_lstm_predictions, fake_hot_numbers, fake_cold_numbers, fake_excluded
    )

    print(f"\n🎯 FINAL RESULT: {final_5}")
    print("="*50)

    return final_5

def test_detailed_logging():
    """Test detailed logging với dữ liệu giả"""
    print("🧪 TESTING DETAILED LOGGING")
    print("="*50)

    # Tạo dữ liệu giả
    predicted_missing = [12, 34, 56, 78, 1]  # Dự đoán
    actual_results = [23, 45, 67, 89, 90, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17]  # Số ra thực tế
    hot_numbers = [23, 45, 67, 12, 34, 56, 78, 1, 5, 80]  # Top 10 hot
    cold_numbers = [15, 25, 35, 40, 50, 60, 70, 75, 77, 79]  # Top 10 cold

    # Tạo predictor và test logging
    predictor = SimplifiedKenoPredictor()

    # Test detailed analysis
    strategy_effectiveness = predictor.log_detailed_analysis(
        predicted_missing, actual_results, hot_numbers, cold_numbers, 51, "10:00:00"
    )

    print(f"\n📊 Strategy effectiveness: {strategy_effectiveness}")
    print("="*50)

    return strategy_effectiveness

def test_combinations_demo():
    """Test demo bộ 4 số với dữ liệu giả"""
    print("🧪 TESTING 4-NUMBER COMBINATIONS")
    print("="*60)

    # Tạo dữ liệu giả
    six_numbers = [4, 9, 51, 27, 63, 15]
    actual_results = [4, 23, 45, 67, 89, 90, 2, 3, 51, 6, 7, 8, 9, 10, 11, 13, 14, 27, 16, 17]  # Giả sử có 4, 51, 9, 27 trúng

    predictor = SimplifiedKenoPredictor()

    # Tạo bộ 4 số
    combinations = predictor.generate_4_number_combinations(six_numbers)

    # Phân tích kết quả
    winning_combos, losing_combos = predictor.analyze_combination_results(combinations, actual_results)

    print(f"\n📊 COMBINATION ANALYSIS:")
    print(f"   Actual results: {actual_results}")
    print(f"   Numbers from our 6 that appeared: {[n for n in six_numbers if n in actual_results]}")

    print(f"\n🏆 WINNING COMBINATIONS ({len(winning_combos)}):")
    for combo in winning_combos:
        print(f"   Bộ {combo['combo_num']:2d}: {combo['numbers']} - {combo['matches']} số đúng")

    print(f"\n❌ LOSING COMBINATIONS ({len(losing_combos)}):")
    for combo in losing_combos:
        print(f"   Bộ {combo['combo_num']:2d}: {combo['numbers']} - {combo['matches']} số đúng")

    # Tính tỷ lệ
    total = len(combinations)
    win_count = len(winning_combos)
    win_rate = (win_count / total) * 100 if total > 0 else 0

    print(f"\n📈 SUMMARY:")
    print(f"   Total combinations: {total}")
    print(f"   Winning combinations: {win_count} ({win_rate:.1f}%)")
    print(f"   Losing combinations: {len(losing_combos)} ({100-win_rate:.1f}%)")

    return {
        'total': total,
        'winning': win_count,
        'losing': len(losing_combos),
        'win_rate': win_rate
    }

def test_time_optimization():
    """Test time optimization với các khung giờ khác nhau"""
    print("🧪 TESTING TIME OPTIMIZATION")
    print("="*60)

    predictor = SimplifiedKenoPredictor()
    six_numbers = [4, 9, 51, 27, 63, 15]
    hot_numbers = [23, 14, 7, 11, 47, 5, 48, 38, 28, 21]
    cold_numbers = [69, 67, 77, 24, 45, 60, 56, 61, 1, 29]

    # Test các khung giờ khác nhau
    test_times = [
        ("13:36", "HIGH Performance"),
        ("12:48", "MEDIUM Performance"),
        ("14:31", "LOW Performance")
    ]

    for test_time, expected_level in test_times:
        print(f"\n🕐 Testing time: {test_time} (Expected: {expected_level})")
        print("-" * 50)

        # Test time optimization
        combinations = predictor.optimize_prediction_by_time(
            six_numbers, hot_numbers, cold_numbers, test_time
        )

        print(f"   Result: {len(combinations)} combinations generated")
        print()

    return True

def test_full_day(test_date="2025-04-01"):
    """Test full ngày với tất cả kì từ 51 đến cuối ngày"""
    print(f"🧪 TESTING FULL DAY: {test_date}")
    print("="*60)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, cần ít nhất 51 kì")
        return None

    print(f"📅 Ngày {test_date}: {len(day_draws)} kì, test từ kì 51 đến kì {len(day_draws)}")

    predictions_count = 0
    perfect_predictions = 0  # ≥4/5 đúng
    good_predictions = 0     # 5/5 đúng
    normal_predictions = 0   # 4/5 đúng
    bad_predictions = 0      # ≤3/5 đúng

    # Test từ kì 51 đến cuối ngày
    for draw_index in range(50, len(day_draws)):
        draw_number = draw_index + 1

        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán (tắt logging để chạy nhanh)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            predicted_missing = predictor.predict(day_results=input_draws)

            sys.stdout = old_stdout

            if predicted_missing:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Tính độ chính xác
                _, correct, _, _, _ = predictor.calculate_accuracy(predicted_missing, actual_results)

                predictions_count += 1

                # Phân loại kết quả
                if correct >= 5:
                    good_predictions += 1
                    perfect_predictions += 1
                    status = "⭐"
                elif correct >= 4:
                    normal_predictions += 1
                    perfect_predictions += 1
                    status = "🔥"
                else:
                    bad_predictions += 1
                    status = "❌" if correct <= 2 else "⚠️"

                print(f"Kì {draw_number:3d} ({actual_time}): {correct}/5 đúng {status}")

        except Exception as e:
            print(f"❌ Lỗi kì {draw_number}: {e}")

    # Tổng kết
    if predictions_count > 0:
        perfect_rate = (perfect_predictions / predictions_count) * 100
        good_rate = (good_predictions / predictions_count) * 100
        normal_rate = (normal_predictions / predictions_count) * 100
        bad_rate = (bad_predictions / predictions_count) * 100

        print(f"\n📊 KẾT QUẢ NGÀY {test_date}:")
        print(f"   Tổng số kì test: {predictions_count}")
        print(f"   Perfect (≥4/5): {perfect_predictions} kì ({perfect_rate:.1f}%)")
        print(f"   Good (5/5): {good_predictions} kì ({good_rate:.1f}%)")
        print(f"   Normal (4/5): {normal_predictions} kì ({normal_rate:.1f}%)")
        print(f"   Bad (≤3/5): {bad_predictions} kì ({bad_rate:.1f}%)")

        return {
            'date': test_date,
            'predictions_count': predictions_count,
            'perfect_predictions': perfect_predictions,
            'perfect_rate': perfect_rate,
            'good_predictions': good_predictions,
            'good_rate': good_rate,
            'normal_predictions': normal_predictions,
            'normal_rate': normal_rate,
            'bad_predictions': bad_predictions,
            'bad_rate': bad_rate
        }

    return None

def test_combinations_real_data(test_date="2025-04-01"):
    """Test bộ 4 số với dữ liệu thực"""
    print(f"🧪 TESTING 4-NUMBER COMBINATIONS - REAL DATA: {test_date}")
    print("="*70)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        print(f"Ngày {test_date} chỉ có {len(day_draws)} kì, cần ít nhất 51 kì")
        return None

    print(f"📅 Ngày {test_date}: {len(day_draws)} kì, test từ kì 51 đến kì {min(60, len(day_draws))}")

    total_combinations = 0
    total_winning_combinations = 0
    total_losing_combinations = 0

    combination_stats = {
        '4_correct': 0,  # 4/4 đúng
        '3_correct': 0,  # 3/4 đúng
        '2_correct': 0,  # 2/4 đúng
        '1_correct': 0,  # 1/4 đúng
        '0_correct': 0   # 0/4 đúng
    }

    # Test 10 kì đầu tiên để demo
    max_test = min(60, len(day_draws))

    for draw_index in range(50, max_test):
        draw_number = draw_index + 1

        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán (tắt logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            six_numbers, combinations = predictor.predict(day_results=input_draws)

            sys.stdout = old_stdout

            if six_numbers and combinations:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Phân tích kết quả bộ 4 số
                winning_combos, losing_combos = predictor.analyze_combination_results(combinations, actual_results)

                total_combinations += len(combinations)
                total_winning_combinations += len(winning_combos)
                total_losing_combinations += len(losing_combos)

                # Thống kê chi tiết
                for combo in winning_combos + losing_combos:
                    matches = combo['matches']
                    if matches == 4:
                        combination_stats['4_correct'] += 1
                    elif matches == 3:
                        combination_stats['3_correct'] += 1
                    elif matches == 2:
                        combination_stats['2_correct'] += 1
                    elif matches == 1:
                        combination_stats['1_correct'] += 1
                    else:
                        combination_stats['0_correct'] += 1

                # Tính tỷ lệ trúng cho kì này
                win_rate = (len(winning_combos) / len(combinations)) * 100 if combinations else 0

                # Số từ 6 số dự đoán có trong kết quả
                six_matches = len(set(six_numbers) & set(actual_results))

                print(f"Kì {draw_number:3d} ({actual_time}): {six_matches}/6 số đúng, {len(winning_combos)}/{len(combinations)} bộ trúng ({win_rate:.1f}%)")

        except Exception as e:
            print(f"❌ Lỗi kì {draw_number}: {e}")

    # Tổng kết
    if total_combinations > 0:
        overall_win_rate = (total_winning_combinations / total_combinations) * 100

        print(f"\n📊 TỔNG KẾT NGÀY {test_date}:")
        print(f"   Tổng số bộ 4 số: {total_combinations}")
        print(f"   Bộ trúng (≥2/4): {total_winning_combinations} ({overall_win_rate:.1f}%)")
        print(f"   Bộ trượt (<2/4): {total_losing_combinations} ({100-overall_win_rate:.1f}%)")

        print(f"\n🎯 PHÂN TÍCH CHI TIẾT:")
        print(f"   4/4 đúng: {combination_stats['4_correct']} bộ ({combination_stats['4_correct']/total_combinations*100:.1f}%)")
        print(f"   3/4 đúng: {combination_stats['3_correct']} bộ ({combination_stats['3_correct']/total_combinations*100:.1f}%)")
        print(f"   2/4 đúng: {combination_stats['2_correct']} bộ ({combination_stats['2_correct']/total_combinations*100:.1f}%)")
        print(f"   1/4 đúng: {combination_stats['1_correct']} bộ ({combination_stats['1_correct']/total_combinations*100:.1f}%)")
        print(f"   0/4 đúng: {combination_stats['0_correct']} bộ ({combination_stats['0_correct']/total_combinations*100:.1f}%)")

        return {
            'date': test_date,
            'total_combinations': total_combinations,
            'winning_combinations': total_winning_combinations,
            'losing_combinations': total_losing_combinations,
            'win_rate': overall_win_rate,
            'stats': combination_stats
        }

    return None

def test_comprehensive_4_number_analysis():
    """Test comprehensive bộ 4 số từ 2025-04-01 đến 2025-05-27"""
    print("🧪 COMPREHENSIVE 4-NUMBER ANALYSIS (2025-04-01 to 2025-05-27)")
    print("="*80)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả ngày từ 2025-04-01
    test_dates = predictor.get_test_dates()

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return None

    print(f"📅 Sẽ test {len(test_dates)} ngày từ {test_dates[0]} đến {test_dates[-1]}")

    # Thống kê tổng hợp
    total_stats = {
        'total_combinations': 0,
        'total_4_correct': 0,
        'total_3_correct': 0,
        'total_2_correct': 0,
        'total_1_correct': 0,
        'total_0_correct': 0,
        'total_winning': 0,
        'total_losing': 0,
        'days_tested': 0,
        'periods_tested': 0
    }

    # Thống kê theo khung giờ
    time_slot_stats = {}

    # Thống kê theo performance level
    performance_stats = {
        'high': {'combinations': 0, '4_correct': 0, 'winning': 0},
        'medium': {'combinations': 0, '4_correct': 0, 'winning': 0},
        'low': {'combinations': 0, '4_correct': 0, 'winning': 0}
    }

    daily_results = []

    # Test từng ngày (giới hạn 10 kì/ngày để tránh quá lâu)
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n[{i}/{len(test_dates)}] Testing {test_date}...")

        day_result = test_single_day_4_numbers(test_date, max_periods=10)

        if day_result:
            daily_results.append(day_result)
            total_stats['days_tested'] += 1
            total_stats['periods_tested'] += day_result['periods_tested']
            total_stats['total_combinations'] += day_result['total_combinations']
            total_stats['total_4_correct'] += day_result['4_correct']
            total_stats['total_3_correct'] += day_result['3_correct']
            total_stats['total_2_correct'] += day_result['2_correct']
            total_stats['total_1_correct'] += day_result['1_correct']
            total_stats['total_0_correct'] += day_result['0_correct']
            total_stats['total_winning'] += day_result['winning_combinations']
            total_stats['total_losing'] += day_result['losing_combinations']

            # Cập nhật thống kê khung giờ
            for time_slot, stats in day_result.get('time_stats', {}).items():
                if time_slot not in time_slot_stats:
                    time_slot_stats[time_slot] = {'combinations': 0, '4_correct': 0, 'winning': 0}
                time_slot_stats[time_slot]['combinations'] += stats['combinations']
                time_slot_stats[time_slot]['4_correct'] += stats['4_correct']
                time_slot_stats[time_slot]['winning'] += stats['winning']

            # Cập nhật thống kê performance level
            for level, stats in day_result.get('performance_stats', {}).items():
                if level in performance_stats:
                    performance_stats[level]['combinations'] += stats['combinations']
                    performance_stats[level]['4_correct'] += stats['4_correct']
                    performance_stats[level]['winning'] += stats['winning']

            # In kết quả ngắn gọn
            rate_4_correct = (day_result['4_correct'] / day_result['total_combinations']) * 100 if day_result['total_combinations'] > 0 else 0
            win_rate = (day_result['winning_combinations'] / day_result['total_combinations']) * 100 if day_result['total_combinations'] > 0 else 0
            print(f"   {test_date}: {day_result['periods_tested']} kì, {day_result['total_combinations']} bộ")
            print(f"     4/4 đúng: {day_result['4_correct']} ({rate_4_correct:.1f}%)")
            print(f"     Trúng: {day_result['winning_combinations']} ({win_rate:.1f}%)")

    # Phân tích tổng hợp
    if total_stats['total_combinations'] > 0:
        analyze_comprehensive_results(total_stats, time_slot_stats, performance_stats, daily_results)

    return {
        'total_stats': total_stats,
        'time_slot_stats': time_slot_stats,
        'performance_stats': performance_stats,
        'daily_results': daily_results
    }

def test_single_day_4_numbers(test_date, max_periods=10):
    """Test bộ 4 số cho một ngày (giới hạn số kì)"""
    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        return None

    # Giới hạn số kì test
    max_test = min(50 + max_periods, len(day_draws))

    day_stats = {
        'date': test_date,
        'periods_tested': 0,
        'total_combinations': 0,
        '4_correct': 0,
        '3_correct': 0,
        '2_correct': 0,
        '1_correct': 0,
        '0_correct': 0,
        'winning_combinations': 0,
        'losing_combinations': 0,
        'time_stats': {},
        'performance_stats': {'high': {'combinations': 0, '4_correct': 0, 'winning': 0},
                             'medium': {'combinations': 0, '4_correct': 0, 'winning': 0},
                             'low': {'combinations': 0, '4_correct': 0, 'winning': 0}}
    }

    # Test từ kì 51
    for draw_index in range(50, max_test):
        draw_number = draw_index + 1

        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán (tắt logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            six_numbers, combinations = predictor.predict(day_results=input_draws)

            sys.stdout = old_stdout

            if six_numbers and combinations:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Phân tích kết quả
                winning_combos, losing_combos = predictor.analyze_combination_results(combinations, actual_results)

                day_stats['periods_tested'] += 1
                day_stats['total_combinations'] += len(combinations)
                day_stats['winning_combinations'] += len(winning_combos)
                day_stats['losing_combinations'] += len(losing_combos)

                # Thống kê chi tiết
                for combo in winning_combos + losing_combos:
                    matches = combo['matches']
                    if matches == 4:
                        day_stats['4_correct'] += 1
                    elif matches == 3:
                        day_stats['3_correct'] += 1
                    elif matches == 2:
                        day_stats['2_correct'] += 1
                    elif matches == 1:
                        day_stats['1_correct'] += 1
                    else:
                        day_stats['0_correct'] += 1

                # Thống kê theo khung giờ
                hour = int(actual_time.split(':')[0])
                time_slot = f"{hour:02d}:xx"

                if time_slot not in day_stats['time_stats']:
                    day_stats['time_stats'][time_slot] = {'combinations': 0, '4_correct': 0, 'winning': 0}

                day_stats['time_stats'][time_slot]['combinations'] += len(combinations)
                day_stats['time_stats'][time_slot]['4_correct'] += sum(1 for combo in winning_combos + losing_combos if combo['matches'] == 4)
                day_stats['time_stats'][time_slot]['winning'] += len(winning_combos)

                # Thống kê theo performance level
                performance_level, _ = predictor.get_time_performance_level(actual_time)
                day_stats['performance_stats'][performance_level]['combinations'] += len(combinations)
                day_stats['performance_stats'][performance_level]['4_correct'] += sum(1 for combo in winning_combos + losing_combos if combo['matches'] == 4)
                day_stats['performance_stats'][performance_level]['winning'] += len(winning_combos)

        except Exception:
            continue

    return day_stats if day_stats['periods_tested'] > 0 else None

def test_statistical_approach_comprehensive():
    """Test Statistical approach với tất cả dữ liệu từ 2025-04-01"""
    print("🧪 COMPREHENSIVE STATISTICAL APPROACH TEST")
    print("="*80)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả ngày từ 2025-04-01
    test_dates = predictor.get_test_dates()

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return None

    print(f"📅 Sẽ test {len(test_dates)} ngày từ {test_dates[0]} đến {test_dates[-1]}")

    # Thống kê tổng hợp
    total_stats = {
        'total_predictions': 0,
        'total_4_correct': 0,
        'total_3_correct': 0,
        'total_2_correct': 0,
        'total_1_correct': 0,
        'total_0_correct': 0,
        'days_tested': 0,
        'periods_tested': 0
    }

    # Thống kê theo khung giờ
    time_slot_stats = {}

    daily_results = []

    # Test từng ngày (giới hạn 10 kì/ngày để tránh quá lâu)
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n[{i}/{len(test_dates)}] Testing {test_date}...")

        day_result = test_single_day_statistical(test_date, max_periods=10)

        if day_result:
            daily_results.append(day_result)
            total_stats['days_tested'] += 1
            total_stats['periods_tested'] += day_result['periods_tested']
            total_stats['total_predictions'] += day_result['total_predictions']
            total_stats['total_4_correct'] += day_result['4_correct']
            total_stats['total_3_correct'] += day_result['3_correct']
            total_stats['total_2_correct'] += day_result['2_correct']
            total_stats['total_1_correct'] += day_result['1_correct']
            total_stats['total_0_correct'] += day_result['0_correct']

            # Cập nhật thống kê khung giờ
            for time_slot, stats in day_result.get('time_stats', {}).items():
                if time_slot not in time_slot_stats:
                    time_slot_stats[time_slot] = {'predictions': 0, '4_correct': 0}
                time_slot_stats[time_slot]['predictions'] += stats['predictions']
                time_slot_stats[time_slot]['4_correct'] += stats['4_correct']

            # In kết quả ngắn gọn
            rate_4_correct = (day_result['4_correct'] / day_result['total_predictions']) * 100 if day_result['total_predictions'] > 0 else 0
            print(f"   {test_date}: {day_result['periods_tested']} kì, {day_result['total_predictions']} predictions")
            print(f"     4/4 đúng: {day_result['4_correct']} ({rate_4_correct:.1f}%)")

    # Phân tích tổng hợp
    if total_stats['total_predictions'] > 0:
        analyze_statistical_results(total_stats, time_slot_stats, daily_results)

    return {
        'total_stats': total_stats,
        'time_slot_stats': time_slot_stats,
        'daily_results': daily_results
    }

def test_single_day_statistical(test_date, max_periods=10):
    """Test Statistical approach cho một ngày"""
    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        return None

    # Giới hạn số kì test
    max_test = min(50 + max_periods, len(day_draws))

    day_stats = {
        'date': test_date,
        'periods_tested': 0,
        'total_predictions': 0,
        '4_correct': 0,
        '3_correct': 0,
        '2_correct': 0,
        '1_correct': 0,
        '0_correct': 0,
        'time_stats': {}
    }

    # Test từ kì 51
    for draw_index in range(50, max_test):
        draw_number = draw_index + 1

        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán Statistical (tắt logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            six_numbers, combinations = predictor.predict_statistical(day_results=input_draws)

            sys.stdout = old_stdout

            if six_numbers and combinations:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Lấy bộ 4 số dự đoán
                predicted_4 = combinations[0]

                # Tính số trượt thực tế
                actual_missing = set(range(1, 81)) - set(actual_results)

                # Tính số dự đoán đúng
                correct_count = len(set(predicted_4) & actual_missing)

                day_stats['periods_tested'] += 1
                day_stats['total_predictions'] += 1

                # Phân loại kết quả
                if correct_count == 4:
                    day_stats['4_correct'] += 1
                elif correct_count == 3:
                    day_stats['3_correct'] += 1
                elif correct_count == 2:
                    day_stats['2_correct'] += 1
                elif correct_count == 1:
                    day_stats['1_correct'] += 1
                else:
                    day_stats['0_correct'] += 1

                # Thống kê theo khung giờ
                hour = int(actual_time.split(':')[0])
                time_slot = f"{hour:02d}:xx"

                if time_slot not in day_stats['time_stats']:
                    day_stats['time_stats'][time_slot] = {'predictions': 0, '4_correct': 0}

                day_stats['time_stats'][time_slot]['predictions'] += 1
                if correct_count == 4:
                    day_stats['time_stats'][time_slot]['4_correct'] += 1

        except Exception:
            continue

    return day_stats if day_stats['periods_tested'] > 0 else None

def analyze_statistical_results(total_stats, time_slot_stats, daily_results):
    """Phân tích kết quả Statistical approach"""
    print(f"\n" + "="*80)
    print("📊 STATISTICAL APPROACH COMPREHENSIVE RESULTS")
    print("="*80)

    # Thống kê tổng quan
    total_predictions = total_stats['total_predictions']
    rate_4_correct = (total_stats['total_4_correct'] / total_predictions) * 100
    rate_3_correct = (total_stats['total_3_correct'] / total_predictions) * 100
    rate_2_correct = (total_stats['total_2_correct'] / total_predictions) * 100
    rate_1_correct = (total_stats['total_1_correct'] / total_predictions) * 100
    rate_0_correct = (total_stats['total_0_correct'] / total_predictions) * 100

    print(f"🎯 TỔNG QUAN STATISTICAL APPROACH:")
    print(f"   Số ngày test: {total_stats['days_tested']}")
    print(f"   Số kì test: {total_stats['periods_tested']}")
    print(f"   Tổng số predictions: {total_predictions}")
    print(f"   4/4 đúng: {total_stats['total_4_correct']} ({rate_4_correct:.2f}%)")
    print(f"   3/4 đúng: {total_stats['total_3_correct']} ({rate_3_correct:.1f}%)")
    print(f"   2/4 đúng: {total_stats['total_2_correct']} ({rate_2_correct:.1f}%)")
    print(f"   1/4 đúng: {total_stats['total_1_correct']} ({rate_1_correct:.1f}%)")
    print(f"   0/4 đúng: {total_stats['total_0_correct']} ({rate_0_correct:.1f}%)")

    # So sánh với LSTM approach
    print(f"\n📈 SO SÁNH VỚI LSTM APPROACH:")
    print(f"   LSTM 4/4 rate: 0.03% (1/3360 bộ)")
    print(f"   Statistical 4/4 rate: {rate_4_correct:.2f}% ({total_stats['total_4_correct']}/{total_predictions} predictions)")

    if rate_4_correct > 0.03:
        improvement = rate_4_correct / 0.03
        print(f"   🔥 IMPROVEMENT: {improvement:.1f}x better than LSTM!")
    else:
        print(f"   ⚠️ Still need improvement")

    # Phân tích theo khung giờ
    print(f"\n⏰ PHÂN TÍCH THEO KHUNG GIỜ:")
    if time_slot_stats:
        sorted_time_slots = []
        for time_slot, stats in time_slot_stats.items():
            if stats['predictions'] >= 10:  # Chỉ phân tích khung giờ có đủ dữ liệu
                rate_4_correct_slot = (stats['4_correct'] / stats['predictions']) * 100
                sorted_time_slots.append({
                    'time_slot': time_slot,
                    'predictions': stats['predictions'],
                    '4_correct': stats['4_correct'],
                    'rate_4_correct': rate_4_correct_slot
                })

        sorted_time_slots.sort(key=lambda x: x['rate_4_correct'], reverse=True)

        print(f"   {'Khung giờ':<10} {'Predictions':<12} {'4/4 đúng':<15}")
        print(f"   {'-'*10} {'-'*12} {'-'*15}")

        for slot in sorted_time_slots:
            print(f"   {slot['time_slot']:<10} {slot['predictions']:<12} "
                  f"{slot['4_correct']} ({slot['rate_4_correct']:.2f}%)")

    # Top 10 ngày tốt nhất
    print(f"\n🏆 TOP 10 NGÀY TỐT NHẤT CHO 4/4 ĐÚNG:")
    sorted_days = []
    for day in daily_results:
        if day['total_predictions'] > 0:
            rate_4_correct_day = (day['4_correct'] / day['total_predictions']) * 100
            sorted_days.append({
                'date': day['date'],
                '4_correct': day['4_correct'],
                'total_predictions': day['total_predictions'],
                'rate_4_correct': rate_4_correct_day
            })

    sorted_days.sort(key=lambda x: x['rate_4_correct'], reverse=True)

    for i, day in enumerate(sorted_days[:10], 1):
        print(f"   {i:2d}. {day['date']}: {day['rate_4_correct']:.1f}% "
              f"({day['4_correct']}/{day['total_predictions']} predictions)")

    # Kết luận
    print(f"\n💡 KẾT LUẬN STATISTICAL APPROACH:")

    if rate_4_correct >= 2.0:
        print(f"   ✅ Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% là XUẤT SẮC!")
        print(f"   🎯 Statistical approach THÀNH CÔNG!")
    elif rate_4_correct >= 1.0:
        print(f"   🔥 Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% là TỐT, cải thiện đáng kể!")
        print(f"   📈 Có thể tối ưu thêm để đạt >2%")
    elif rate_4_correct > 0.03:
        print(f"   ⚠️ Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% cải thiện so với LSTM nhưng chưa đủ")
        print(f"   🔧 Cần điều chỉnh thêm statistical parameters")
    else:
        print(f"   ❌ Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% vẫn thấp, cần approach khác")

    print("="*80)

def test_statistical_detailed_all_data():
    """Test Statistical approach chi tiết từ 2025-01-04 đến nay"""
    print("🧪 DETAILED STATISTICAL TEST - ALL DATA FROM 2025-01-04")
    print("="*100)

    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả ngày từ 2025-01-04
    test_dates = predictor.get_all_test_dates_from_jan()

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return None

    print(f"📅 Sẽ test {len(test_dates)} ngày từ {test_dates[0]} đến {test_dates[-1]}")
    print(f"📝 Format: [Ngày] Kì XX (HH:MM) | Dự đoán: [a,b,c,d] | Thực tế trượt: [x,y,z,...] | Kết quả: X/4")
    print("="*100)

    # Thống kê tổng hợp
    total_stats = {
        'total_predictions': 0,
        'total_4_correct': 0,
        'total_3_correct': 0,
        'total_2_correct': 0,
        'total_1_correct': 0,
        'total_0_correct': 0,
        'days_tested': 0,
        'periods_tested': 0
    }

    # Test từng ngày
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n📅 [{i}/{len(test_dates)}] {test_date}")
        print("-" * 80)

        day_result = test_single_day_detailed(test_date, predictor)

        if day_result:
            total_stats['days_tested'] += 1
            total_stats['periods_tested'] += day_result['periods_tested']
            total_stats['total_predictions'] += day_result['total_predictions']
            total_stats['total_4_correct'] += day_result['4_correct']
            total_stats['total_3_correct'] += day_result['3_correct']
            total_stats['total_2_correct'] += day_result['2_correct']
            total_stats['total_1_correct'] += day_result['1_correct']
            total_stats['total_0_correct'] += day_result['0_correct']

    # Tổng kết cuối cùng
    print(f"\n" + "="*100)
    print("📊 TỔNG KẾT CUỐI CÙNG")
    print("="*100)

    if total_stats['total_predictions'] > 0:
        rate_4_correct = (total_stats['total_4_correct'] / total_stats['total_predictions']) * 100
        rate_3_correct = (total_stats['total_3_correct'] / total_stats['total_predictions']) * 100
        rate_2_correct = (total_stats['total_2_correct'] / total_stats['total_predictions']) * 100

        print(f"🎯 THỐNG KÊ TỔNG HỢP:")
        print(f"   Số ngày test: {total_stats['days_tested']}")
        print(f"   Số kì test: {total_stats['periods_tested']}")
        print(f"   Tổng predictions: {total_stats['total_predictions']}")
        print(f"   4/4 đúng: {total_stats['total_4_correct']} ({rate_4_correct:.2f}%)")
        print(f"   3/4 đúng: {total_stats['total_3_correct']} ({rate_3_correct:.1f}%)")
        print(f"   2/4 đúng: {total_stats['total_2_correct']} ({rate_2_correct:.1f}%)")
        print(f"   1/4 đúng: {total_stats['total_1_correct']}")
        print(f"   0/4 đúng: {total_stats['total_0_correct']}")

        print(f"\n💰 ĐÁNH GIÁ HIỆU SUẤT:")
        if rate_4_correct >= 25.0:
            print(f"   ✅ XUẤT SẮC! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% rất cao")
        elif rate_4_correct >= 15.0:
            print(f"   🔥 RẤT TỐT! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% khả quan")
        elif rate_4_correct >= 5.0:
            print(f"   ⚠️ CHẤP NHẬN ĐƯỢC! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}%")
        else:
            print(f"   ❌ CẦN CẢI THIỆN! Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% thấp")

    print("="*100)
    return total_stats

def test_single_day_detailed(test_date, predictor):
    """Test chi tiết một ngày với output đầy đủ"""
    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        print(f"   ⚠️ Chỉ có {len(day_draws)} kì, cần ít nhất 51 kì")
        return None

    day_stats = {
        'date': test_date,
        'periods_tested': 0,
        'total_predictions': 0,
        '4_correct': 0,
        '3_correct': 0,
        '2_correct': 0,
        '1_correct': 0,
        '0_correct': 0
    }

    # Test từ kì 51 đến cuối ngày
    for draw_index in range(50, len(day_draws)):
        draw_number = draw_index + 1

        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán Statistical (tắt logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            six_numbers, combinations = predictor.predict_statistical(day_results=input_draws)

            sys.stdout = old_stdout

            if six_numbers and combinations:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Lấy bộ 4 số dự đoán
                predicted_4 = combinations[0]

                # Tính số trượt thực tế
                actual_missing = sorted(list(set(range(1, 81)) - set(actual_results)))

                # Tính số dự đoán đúng
                correct_numbers = set(predicted_4) & set(actual_missing)
                correct_count = len(correct_numbers)

                # Tạo status icon
                if correct_count == 4:
                    status = "🎯"
                    day_stats['4_correct'] += 1
                elif correct_count == 3:
                    status = "🔥"
                    day_stats['3_correct'] += 1
                elif correct_count == 2:
                    status = "✅"
                    day_stats['2_correct'] += 1
                elif correct_count == 1:
                    status = "⚠️"
                    day_stats['1_correct'] += 1
                else:
                    status = "❌"
                    day_stats['0_correct'] += 1

                day_stats['periods_tested'] += 1
                day_stats['total_predictions'] += 1

                # In kết quả chi tiết
                print(f"   Kì {draw_number:3d} ({actual_time}) | "
                      f"Dự đoán: {predicted_4} | "
                      f"Trượt thực tế: {actual_missing[:10]}{'...' if len(actual_missing) > 10 else ''} | "
                      f"Đúng: {sorted(list(correct_numbers))} | "
                      f"Kết quả: {correct_count}/4 {status}")

        except Exception as e:
            print(f"   ❌ Lỗi kì {draw_number}: {str(e)[:50]}")
            continue

    # Tổng kết ngày
    if day_stats['periods_tested'] > 0:
        rate_4_correct = (day_stats['4_correct'] / day_stats['total_predictions']) * 100
        print(f"\n   📊 Tổng kết {test_date}: {day_stats['periods_tested']} kì, "
              f"4/4 đúng: {day_stats['4_correct']}/{day_stats['total_predictions']} ({rate_4_correct:.1f}%)")

    return day_stats if day_stats['periods_tested'] > 0 else None

def analyze_comprehensive_results(total_stats, time_slot_stats, performance_stats, daily_results):
    """Phân tích comprehensive kết quả tất cả ngày"""
    print(f"\n" + "="*80)
    print("📊 COMPREHENSIVE ANALYSIS RESULTS")
    print("="*80)

    # Thống kê tổng quan
    total_combinations = total_stats['total_combinations']
    rate_4_correct = (total_stats['total_4_correct'] / total_combinations) * 100
    rate_3_correct = (total_stats['total_3_correct'] / total_combinations) * 100
    rate_2_correct = (total_stats['total_2_correct'] / total_combinations) * 100
    win_rate = (total_stats['total_winning'] / total_combinations) * 100

    print(f"🎯 TỔNG QUAN:")
    print(f"   Số ngày test: {total_stats['days_tested']}")
    print(f"   Số kì test: {total_stats['periods_tested']}")
    print(f"   Tổng số bộ 4 số: {total_combinations}")
    print(f"   4/4 đúng: {total_stats['total_4_correct']} ({rate_4_correct:.2f}%)")
    print(f"   3/4 đúng: {total_stats['total_3_correct']} ({rate_3_correct:.1f}%)")
    print(f"   2/4 đúng: {total_stats['total_2_correct']} ({rate_2_correct:.1f}%)")
    print(f"   Tỷ lệ trúng (≥2/4): {total_stats['total_winning']} ({win_rate:.1f}%)")

    # Phân tích theo khung giờ
    print(f"\n⏰ PHÂN TÍCH THEO KHUNG GIỜ:")
    if time_slot_stats:
        # Sắp xếp theo tỷ lệ 4/4 đúng
        sorted_time_slots = []
        for time_slot, stats in time_slot_stats.items():
            if stats['combinations'] >= 20:  # Chỉ phân tích khung giờ có đủ dữ liệu
                rate_4_correct_slot = (stats['4_correct'] / stats['combinations']) * 100
                win_rate_slot = (stats['winning'] / stats['combinations']) * 100
                sorted_time_slots.append({
                    'time_slot': time_slot,
                    'combinations': stats['combinations'],
                    '4_correct': stats['4_correct'],
                    'rate_4_correct': rate_4_correct_slot,
                    'winning': stats['winning'],
                    'win_rate': win_rate_slot
                })

        sorted_time_slots.sort(key=lambda x: x['rate_4_correct'], reverse=True)

        print(f"   {'Khung giờ':<10} {'Tổng bộ':<10} {'4/4 đúng':<15} {'Tỷ lệ trúng':<15}")
        print(f"   {'-'*10} {'-'*10} {'-'*15} {'-'*15}")

        for slot in sorted_time_slots:
            print(f"   {slot['time_slot']:<10} {slot['combinations']:<10} "
                  f"{slot['4_correct']} ({slot['rate_4_correct']:.2f}%){'':<5} "
                  f"{slot['winning']} ({slot['win_rate']:.1f}%)")

        # Top 3 khung giờ tốt nhất cho 4/4
        print(f"\n🏆 TOP 3 KHUNG GIỜ TỐT NHẤT CHO 4/4 ĐÚNG:")
        for i, slot in enumerate(sorted_time_slots[:3], 1):
            print(f"   {i}. {slot['time_slot']}: {slot['rate_4_correct']:.2f}% "
                  f"({slot['4_correct']}/{slot['combinations']} bộ)")

    # Phân tích theo performance level
    print(f"\n🎯 PHÂN TÍCH THEO PERFORMANCE LEVEL:")
    for level in ['high', 'medium', 'low']:
        stats = performance_stats[level]
        if stats['combinations'] > 0:
            rate_4_correct_perf = (stats['4_correct'] / stats['combinations']) * 100
            win_rate_perf = (stats['winning'] / stats['combinations']) * 100
            print(f"   {level.upper():<8}: {stats['combinations']} bộ, "
                  f"4/4 đúng: {stats['4_correct']} ({rate_4_correct_perf:.2f}%), "
                  f"Trúng: {stats['winning']} ({win_rate_perf:.1f}%)")

    # Top 10 ngày tốt nhất
    print(f"\n🏆 TOP 10 NGÀY TỐT NHẤT CHO 4/4 ĐÚNG:")
    sorted_days = []
    for day in daily_results:
        if day['total_combinations'] > 0:
            rate_4_correct_day = (day['4_correct'] / day['total_combinations']) * 100
            sorted_days.append({
                'date': day['date'],
                '4_correct': day['4_correct'],
                'total_combinations': day['total_combinations'],
                'rate_4_correct': rate_4_correct_day
            })

    sorted_days.sort(key=lambda x: x['rate_4_correct'], reverse=True)

    for i, day in enumerate(sorted_days[:10], 1):
        print(f"   {i:2d}. {day['date']}: {day['rate_4_correct']:.2f}% "
              f"({day['4_correct']}/{day['total_combinations']} bộ)")

    # Kết luận và khuyến nghị
    print(f"\n💡 KẾT LUẬN VÀ KHUYẾN NGHỊ:")

    if rate_4_correct >= 2.0:
        print(f"   ✅ Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% là RẤT TỐT cho keno")
    elif rate_4_correct >= 1.0:
        print(f"   🔥 Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% là TỐT, có thể cải thiện thêm")
    else:
        print(f"   ⚠️ Tỷ lệ 4/4 đúng {rate_4_correct:.2f}% cần cải thiện")

    # Khuyến nghị dựa trên performance level
    best_performance = max(performance_stats.items(),
                          key=lambda x: x[1]['4_correct']/x[1]['combinations'] if x[1]['combinations'] > 0 else 0)

    if best_performance[1]['combinations'] > 0:
        best_rate = (best_performance[1]['4_correct'] / best_performance[1]['combinations']) * 100
        print(f"   🎯 Performance level tốt nhất: {best_performance[0].upper()} ({best_rate:.2f}%)")

    # Khuyến nghị khung giờ
    if sorted_time_slots:
        best_time = sorted_time_slots[0]
        print(f"   ⏰ Khung giờ tối ưu: {best_time['time_slot']} ({best_time['rate_4_correct']:.2f}%)")

    print("="*80)

def test_all_days_from_2025_04_01():
    """Test và phân tích tất cả ngày từ 2025-04-01 đến hiện tại"""
    print("🧪 TESTING ALL DAYS FROM 2025-04-01")
    print("="*80)

    predictor = SimplifiedKenoPredictor()

    # Lấy danh sách tất cả ngày
    test_dates = predictor.get_test_dates()

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return

    print(f"📅 Sẽ test {len(test_dates)} ngày từ {test_dates[0]} đến {test_dates[-1]}")

    all_results = []
    total_predictions = 0
    total_perfect = 0
    total_good = 0
    total_normal = 0
    total_bad = 0

    # Test từng ngày
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n[{i}/{len(test_dates)}] Testing {test_date}...")

        result = test_full_day_silent(test_date)

        if result:
            all_results.append(result)
            total_predictions += result['predictions_count']
            total_perfect += result['perfect_predictions']
            total_good += result['good_predictions']
            total_normal += result['normal_predictions']
            total_bad += result['bad_predictions']

            # In kết quả chi tiết (bỏ Perfect)
            print(f"   {test_date}: {result['predictions_count']} kì")
            print(f"     ⭐ Good (5/5): {result['good_rate']:.1f}% ({result['good_predictions']} kì)")
            print(f"     🔥 Normal (4/5): {result['normal_rate']:.1f}% ({result['normal_predictions']} kì)")
            print(f"     Bad (≤3/5): {result['bad_rate']:.1f}% ({result['bad_predictions']} kì)")

    # Phân tích tổng hợp
    if all_results:
        analyze_all_results(all_results, total_predictions, total_perfect, total_good, total_normal, total_bad)

    return all_results

def test_full_day_silent(test_date):
    """Test full ngày nhưng không in chi tiết (để chạy nhanh)"""
    predictor = SimplifiedKenoPredictor()

    # Lấy tất cả kì của ngày
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        return None

    predictions_count = 0
    perfect_predictions = 0
    good_predictions = 0
    normal_predictions = 0
    bad_predictions = 0

    # Thống kê theo khung giờ
    time_stats = {}

    # Test từ kì 51 đến cuối ngày
    for draw_index in range(50, len(day_draws)):
        # Lấy dữ liệu từ kì 1 đến kì hiện tại - 1
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán (tắt hoàn toàn logging)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            predicted_missing = predictor.predict(day_results=input_draws)

            sys.stdout = old_stdout

            if predicted_missing:
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']

                # Tính độ chính xác
                _, correct, _, _, _ = predictor.calculate_accuracy(predicted_missing, actual_results)

                predictions_count += 1

                # Phân loại kết quả
                if correct >= 5:
                    good_predictions += 1
                    perfect_predictions += 1
                elif correct >= 4:
                    normal_predictions += 1
                    perfect_predictions += 1
                else:
                    bad_predictions += 1

                # Thống kê theo khung giờ
                hour = int(actual_time.split(':')[0])
                time_slot = f"{hour:02d}:xx"

                if time_slot not in time_stats:
                    time_stats[time_slot] = {'total': 0, 'good': 0, 'normal': 0, 'bad': 0}

                time_stats[time_slot]['total'] += 1
                if correct >= 5:
                    time_stats[time_slot]['good'] += 1
                elif correct >= 4:
                    time_stats[time_slot]['normal'] += 1
                else:
                    time_stats[time_slot]['bad'] += 1

        except Exception:
            continue

    # Tính tỷ lệ
    if predictions_count > 0:
        perfect_rate = (perfect_predictions / predictions_count) * 100
        good_rate = (good_predictions / predictions_count) * 100
        normal_rate = (normal_predictions / predictions_count) * 100
        bad_rate = (bad_predictions / predictions_count) * 100

        return {
            'date': test_date,
            'predictions_count': predictions_count,
            'perfect_predictions': perfect_predictions,
            'perfect_rate': perfect_rate,
            'good_predictions': good_predictions,
            'good_rate': good_rate,
            'normal_predictions': normal_predictions,
            'normal_rate': normal_rate,
            'bad_predictions': bad_predictions,
            'bad_rate': bad_rate,
            'time_stats': time_stats
        }

    return None

def analyze_all_results(all_results, total_predictions, total_perfect, total_good, total_normal, total_bad):
    """Phân tích chi tiết kết quả tất cả ngày"""
    print(f"\n" + "="*80)
    print("📊 PHÂN TÍCH TỔNG HỢP TẤT CẢ NGÀY")
    print("="*80)

    # Thống kê tổng quan
    overall_perfect_rate = (total_perfect / total_predictions) * 100
    overall_good_rate = (total_good / total_predictions) * 100
    overall_normal_rate = (total_normal / total_predictions) * 100
    overall_bad_rate = (total_bad / total_predictions) * 100

    print(f"🎯 TỔNG QUAN:")
    print(f"   Tổng số ngày test: {len(all_results)}")
    print(f"   Tổng số kì test: {total_predictions}")
    print(f"   Perfect (≥4/5): {total_perfect} kì ({overall_perfect_rate:.1f}%)")
    print(f"     ⭐ Good (5/5): {total_good} kì ({overall_good_rate:.1f}%)")
    print(f"     🔥 Normal (4/5): {total_normal} kì ({overall_normal_rate:.1f}%)")
    print(f"   Bad (≤3/5): {total_bad} kì ({overall_bad_rate:.1f}%)")

    # Top 10 ngày tốt nhất
    print(f"\n🏆 TOP 10 NGÀY TỐT NHẤT (Perfect Rate):")
    sorted_by_perfect = sorted(all_results, key=lambda x: x['perfect_rate'], reverse=True)
    for i, result in enumerate(sorted_by_perfect[:10], 1):
        print(f"   {i:2d}. {result['date']}: {result['perfect_rate']:.1f}% ({result['perfect_predictions']}/{result['predictions_count']} kì)")

    # Top 10 ngày có nhiều Good nhất
    print(f"\n⭐ TOP 10 NGÀY NHIỀU GOOD NHẤT (5/5 đúng):")
    sorted_by_good = sorted(all_results, key=lambda x: x['good_rate'], reverse=True)
    for i, result in enumerate(sorted_by_good[:10], 1):
        print(f"   {i:2d}. {result['date']}: {result['good_rate']:.1f}% ({result['good_predictions']}/{result['predictions_count']} kì)")

    # Phân tích xu hướng theo thời gian
    print(f"\n📈 XU HƯỚNG THEO THỜI GIAN:")

    # Chia thành 3 giai đoạn
    total_days = len(all_results)
    period_size = total_days // 3

    periods = [
        ("Giai đoạn đầu", all_results[:period_size]),
        ("Giai đoạn giữa", all_results[period_size:period_size*2]),
        ("Giai đoạn cuối", all_results[period_size*2:])
    ]

    for period_name, period_data in periods:
        if period_data:
            period_perfect = sum(r['perfect_predictions'] for r in period_data)
            period_total = sum(r['predictions_count'] for r in period_data)
            period_rate = (period_perfect / period_total) * 100 if period_total > 0 else 0

            start_date = period_data[0]['date']
            end_date = period_data[-1]['date']

            print(f"   {period_name} ({start_date} → {end_date}): {period_rate:.1f}% ({len(period_data)} ngày)")

    # Thống kê phân bố
    print(f"\n📊 PHÂN BỐ KẾT QUẢ CÁC NGÀY:")

    # Nhóm theo mức độ thành công
    excellent_days = [r for r in all_results if r['perfect_rate'] >= 60]  # ≥60%
    good_days = [r for r in all_results if 50 <= r['perfect_rate'] < 60]  # 50-59%
    average_days = [r for r in all_results if 40 <= r['perfect_rate'] < 50]  # 40-49%
    poor_days = [r for r in all_results if r['perfect_rate'] < 40]  # <40%

    print(f"   Xuất sắc (≥60%): {len(excellent_days)} ngày ({len(excellent_days)/len(all_results)*100:.1f}%)")
    print(f"   Tốt (50-59%): {len(good_days)} ngày ({len(good_days)/len(all_results)*100:.1f}%)")
    print(f"   Trung bình (40-49%): {len(average_days)} ngày ({len(average_days)/len(all_results)*100:.1f}%)")
    print(f"   Kém (<40%): {len(poor_days)} ngày ({len(poor_days)/len(all_results)*100:.1f}%)")

    # Kết luận
    print(f"\n💡 KẾT LUẬN:")
    if overall_perfect_rate >= 55:
        print(f"   ✅ Chiến lược rất hiệu quả với {overall_perfect_rate:.1f}% dự đoán ≥4/5 đúng")
    elif overall_perfect_rate >= 45:
        print(f"   🔥 Chiến lược khá tốt với {overall_perfect_rate:.1f}% dự đoán ≥4/5 đúng")
    else:
        print(f"   ⚠️ Chiến lược cần cải thiện với {overall_perfect_rate:.1f}% dự đoán ≥4/5 đúng")

    print(f"   📈 Tỷ lệ dự đoán hoàn hảo (5/5): {overall_good_rate:.1f}%")
    print(f"   🎯 Tổng cộng {len(all_results)} ngày, {total_predictions} kì được test")

    # Phân tích khung giờ tối ưu
    analyze_time_slots(all_results)

    print("="*80)

def analyze_time_slots(all_results):
    """Phân tích khung giờ tối ưu để dự đoán"""
    print(f"\n⏰ PHÂN TÍCH KHUNG GIỜ TỐI ƯU:")

    # Tổng hợp thống kê theo khung giờ
    combined_time_stats = {}

    for result in all_results:
        if 'time_stats' in result:
            for time_slot, stats in result['time_stats'].items():
                if time_slot not in combined_time_stats:
                    combined_time_stats[time_slot] = {'total': 0, 'good': 0, 'normal': 0, 'bad': 0}

                combined_time_stats[time_slot]['total'] += stats['total']
                combined_time_stats[time_slot]['good'] += stats['good']
                combined_time_stats[time_slot]['normal'] += stats['normal']
                combined_time_stats[time_slot]['bad'] += stats['bad']

    if not combined_time_stats:
        print("   Không có dữ liệu khung giờ")
        return

    # Tính tỷ lệ cho từng khung giờ
    time_analysis = []
    for time_slot, stats in combined_time_stats.items():
        if stats['total'] >= 10:  # Chỉ phân tích khung giờ có ít nhất 10 kì
            good_rate = (stats['good'] / stats['total']) * 100
            normal_rate = (stats['normal'] / stats['total']) * 100
            perfect_rate = ((stats['good'] + stats['normal']) / stats['total']) * 100

            time_analysis.append({
                'time_slot': time_slot,
                'total': stats['total'],
                'good_rate': good_rate,
                'normal_rate': normal_rate,
                'perfect_rate': perfect_rate,
                'good_count': stats['good'],
                'normal_count': stats['normal'],
                'bad_count': stats['bad']
            })

    # Sắp xếp theo tỷ lệ Good (5/5) giảm dần
    time_analysis.sort(key=lambda x: x['good_rate'], reverse=True)

    print(f"   📊 BẢNG THỐNG KÊ KHUNG GIỜ (≥10 kì):")
    print(f"   {'Giờ':<6} {'Tổng':<6} {'Good(5/5)':<12} {'Normal(4/5)':<14} {'Perfect(≥4/5)':<15}")
    print(f"   {'-'*6} {'-'*6} {'-'*12} {'-'*14} {'-'*15}")

    for analysis in time_analysis:
        time_str = analysis['time_slot']
        total_str = f"{analysis['total']}"
        good_str = f"{analysis['good_rate']:.1f}%({analysis['good_count']})"
        normal_str = f"{analysis['normal_rate']:.1f}%({analysis['normal_count']})"
        perfect_str = f"{analysis['perfect_rate']:.1f}%"

        print(f"   {time_str:<6} {total_str:<6} {good_str:<12} {normal_str:<14} {perfect_str:<15}")

    # Top 5 khung giờ tốt nhất cho Good (5/5)
    print(f"\n🏆 TOP 5 KHUNG GIỜ TỐT NHẤT CHO GOOD (5/5):")
    for i, analysis in enumerate(time_analysis[:5], 1):
        print(f"   {i}. {analysis['time_slot']}: {analysis['good_rate']:.1f}% ({analysis['good_count']}/{analysis['total']} kì)")

    # Top 5 khung giờ tốt nhất cho Perfect (≥4/5)
    time_analysis_perfect = sorted(time_analysis, key=lambda x: x['perfect_rate'], reverse=True)
    print(f"\n⭐ TOP 5 KHUNG GIỜ TỐT NHẤT CHO PERFECT (≥4/5):")
    for i, analysis in enumerate(time_analysis_perfect[:5], 1):
        print(f"   {i}. {analysis['time_slot']}: {analysis['perfect_rate']:.1f}% ({analysis['good_count']+analysis['normal_count']}/{analysis['total']} kì)")

    # Khuyến nghị khung giờ
    print(f"\n💡 KHUYẾN NGHỊ KHUNG GIỜ:")

    # Khung giờ tốt nhất cho Good
    best_good = time_analysis[0] if time_analysis else None
    if best_good and best_good['good_rate'] >= 25:
        print(f"   🎯 Khung giờ tối ưu cho Good (5/5): {best_good['time_slot']} ({best_good['good_rate']:.1f}%)")

    # Khung giờ tốt nhất cho Perfect
    best_perfect = time_analysis_perfect[0] if time_analysis_perfect else None
    if best_perfect and best_perfect['perfect_rate'] >= 70:
        print(f"   ⭐ Khung giờ tối ưu cho Perfect (≥4/5): {best_perfect['time_slot']} ({best_perfect['perfect_rate']:.1f}%)")

    # Khung giờ nên tránh
    worst_slots = [a for a in time_analysis if a['good_rate'] < 15 and a['perfect_rate'] < 50]
    if worst_slots:
        print(f"   ⚠️ Khung giờ nên tránh:")
        for slot in worst_slots[-3:]:  # 3 khung giờ tệ nhất
            print(f"      {slot['time_slot']}: Good {slot['good_rate']:.1f}%, Perfect {slot['perfect_rate']:.1f}%")

def test_sample_days():
    """Test một vài ngày mẫu để demo nhanh"""
    print("🧪 TESTING SAMPLE DAYS (DEMO)")
    print("="*60)

    predictor = SimplifiedKenoPredictor()

    # Test 5 ngày đầu tiên từ 2025-04-01
    test_dates = predictor.get_test_dates()[:5]

    if not test_dates:
        print("❌ Không có ngày nào để test")
        return

    print(f"📅 Demo test {len(test_dates)} ngày đầu tiên")

    all_results = []
    total_predictions = 0
    total_perfect = 0
    total_good = 0
    total_normal = 0
    total_bad = 0

    # Test từng ngày
    for i, test_date in enumerate(test_dates, 1):
        print(f"\n[{i}/{len(test_dates)}] Testing {test_date}...")

        result = test_full_day_silent(test_date)

        if result:
            all_results.append(result)
            total_predictions += result['predictions_count']
            total_perfect += result['perfect_predictions']
            total_good += result['good_predictions']
            total_normal += result['normal_predictions']
            total_bad += result['bad_predictions']

            # In kết quả chi tiết
            print(f"   {test_date}: {result['predictions_count']} kì")
            print(f"     Perfect (≥4/5): {result['perfect_rate']:.1f}% ({result['perfect_predictions']} kì)")
            print(f"       ⭐ Good (5/5): {result['good_rate']:.1f}% ({result['good_predictions']} kì)")
            print(f"       🔥 Normal (4/5): {result['normal_rate']:.1f}% ({result['normal_predictions']} kì)")
            print(f"     Bad (≤3/5): {result['bad_rate']:.1f}% ({result['bad_predictions']} kì)")

    # Tổng kết demo
    if all_results:
        overall_perfect_rate = (total_perfect / total_predictions) * 100
        overall_good_rate = (total_good / total_predictions) * 100
        overall_normal_rate = (total_normal / total_predictions) * 100
        overall_bad_rate = (total_bad / total_predictions) * 100

        print(f"\n📊 TỔNG KẾT DEMO ({len(all_results)} ngày):")
        print(f"   Tổng số kì test: {total_predictions}")
        print(f"   Perfect (≥4/5): {total_perfect} kì ({overall_perfect_rate:.1f}%)")
        print(f"     ⭐ Good (5/5): {total_good} kì ({overall_good_rate:.1f}%)")
        print(f"     🔥 Normal (4/5): {total_normal} kì ({overall_normal_rate:.1f}%)")
        print(f"   Bad (≤3/5): {total_bad} kì ({overall_bad_rate:.1f}%)")

    return all_results

def test_from_2025_04_01():
    """Test tất cả ngày từ 2025-04-01 (wrapper function)"""
    test_all_days_from_2025_04_01()

def main():
    """Main function"""
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()

        if mode == 'predict':
            predictor = SimplifiedKenoPredictor()
            five_numbers, combinations = predictor.predict()
            if five_numbers:
                print(f"\n✅ 5 số dự đoán: {five_numbers}")
                print(f"✅ 1 bộ 4 số tối ưu: {combinations[0] if combinations else 'None'}")
            else:
                print("\n❌ Không thể dự đoán")
        elif mode == 'predict5':
            predictor = SimplifiedKenoPredictor()
            five_numbers = predictor.predict_5_numbers_only()
            if five_numbers:
                print(f"\n✅ 5 số dự đoán: {five_numbers}")
            else:
                print("\n❌ Không thể dự đoán")
        elif mode == 'testnew':
            predictor = SimplifiedKenoPredictor()
            test_date = sys.argv[2] if len(sys.argv) > 2 else None
            predictor.test_new_system(test_date)
        elif mode == 'test':
            test_from_2025_04_01()
        elif mode == 'testfull':
            test_comprehensive_4_number_analysis()
        elif mode == 'teststat':
            test_statistical_approach_comprehensive()
        elif mode == 'testdetailed':
            test_statistical_detailed_all_data()
        elif mode == 'testdemo':
            test_sample_days()
        elif mode == 'testlogic':
            test_strategy_logic()
        elif mode == 'testlog':
            test_detailed_logging()
        elif mode == 'testcombos':
            test_combinations_demo()
        elif mode == 'testtime':
            test_time_optimization()
        elif mode == 'statistical':
            predict_statistical_demo()
        elif mode == 'testopt':
            test_optimized_4_accuracy()
        elif mode == 'testoptfull':
            test_optimized_system_comprehensive()
        elif mode == 'testprofit':
            test_optimized_detailed_profit_focus()
        elif mode == 'testmartingale':
            test_martingale_strategy()
        elif mode == 'testcombosreal':
            # Test bộ 4 số với dữ liệu thực
            test_date = sys.argv[2] if len(sys.argv) > 2 else "2025-04-01"
            test_combinations_real_data(test_date)
        elif mode == 'testday':
            # Test một ngày cụ thể
            test_date = sys.argv[2] if len(sys.argv) > 2 else "2025-04-01"
            test_full_day(test_date)
        else:
            print("🎯 NEW KENO PREDICTION SYSTEM - FOCUS ON 4/4 ACCURACY")
            print("Sử dụng:")
            print("  python simplified_keno_predictor.py predict      # Dự đoán 5 số + 1 bộ 4 số tối ưu")
            print("  python simplified_keno_predictor.py predict5     # Chỉ dự đoán 5 số")
            print("  python simplified_keno_predictor.py testnew [date] # Test hệ thống mới (mặc định ngày gần nhất)")
            print("  python simplified_keno_predictor.py statistical  # Demo Statistical approach")
            print("  python simplified_keno_predictor.py test         # Test từ 2025-04-01 (tất cả ngày)")
            print("  python simplified_keno_predictor.py testfull     # Test COMPREHENSIVE tất cả ngày (LSTM approach)")
            print("  python simplified_keno_predictor.py teststat     # Test COMPREHENSIVE tất cả ngày (Statistical approach)")
            print("  python simplified_keno_predictor.py testdetailed # Test CHI TIẾT từ 2025-01-04 (in từng kì)")
            print("  python simplified_keno_predictor.py testdemo     # Test demo 5 ngày đầu")
            print("  python simplified_keno_predictor.py testlogic    # Test logic chiến lược")
            print("  python simplified_keno_predictor.py testlog      # Test detailed logging")
            print("  python simplified_keno_predictor.py testcombos   # Test demo bộ 4 số")
            print("  python simplified_keno_predictor.py testtime     # Test time optimization")
            print("  python simplified_keno_predictor.py testopt      # Test optimized 4/4 accuracy system")
            print("  python simplified_keno_predictor.py testoptfull  # Test COMPREHENSIVE optimized system (all data)")
            print("  python simplified_keno_predictor.py testprofit   # Test CHI TIẾT LỢI NHUẬN (chỉ 4/4 = thắng)")
            print("  python simplified_keno_predictor.py testmartingale # Test CHIẾN LƯỢC MARTINGALE")
            print("  python simplified_keno_predictor.py testcombosreal [date] # Test bộ 4 số dữ liệu thực")
            print("  python simplified_keno_predictor.py testday [date] # Test full ngày (mặc định 2025-04-01)")
    else:
        # Mặc định: test hệ thống mới
        predictor = SimplifiedKenoPredictor()
        predictor.test_new_system()

def test_martingale_strategy():
    """Test chiến lược Martingale với dữ liệu thực"""
    print("🧪 TESTING MARTINGALE STRATEGY")
    print("="*80)

    predictor = SimplifiedKenoPredictor()

    # Lấy một ngày để test
    test_date = "2025-05-27"
    day_draws = predictor.get_day_draws(test_date)

    if len(day_draws) < 51:
        print(f"❌ Không đủ dữ liệu cho ngày {test_date}")
        return None

    print(f"📅 Testing Martingale strategy on {test_date}")
    print(f"📊 Available periods: {len(day_draws)}")
    print(f"💰 Base bet: {predictor.martingale_config['base_bet']:,}k")
    print(f"🔢 Multiplier: {predictor.martingale_config['multiplier']}")
    print(f"🎯 Max level: {predictor.martingale_config['max_level']}")
    print("="*80)

    # Simulate betting từ kì 51
    total_invested = 0
    total_won = 0
    win_count = 0
    loss_count = 0
    max_level_reached = 1

    for draw_index in range(50, min(70, len(day_draws))):  # Test 20 kì
        period = draw_index + 1

        # Lấy dữ liệu input
        input_draws = [day_draws[i]['results'] for i in range(draw_index)]

        try:
            # Dự đoán (tắt output)
            import sys
            from io import StringIO
            old_stdout = sys.stdout
            sys.stdout = StringIO()

            five_numbers, combinations = predictor.predict_statistical(day_results=input_draws)

            sys.stdout = old_stdout

            if five_numbers and combinations:
                # Lấy khuyến nghị Martingale
                martingale_rec = predictor.get_martingale_recommendation()
                bet_amount = martingale_rec['bet_amount']

                # Kết quả thực tế
                actual_results = day_draws[draw_index]['results']
                actual_time = day_draws[draw_index]['time']
                predicted_4 = combinations[0]

                # Tính số trượt thực tế
                actual_missing = set(range(1, 81)) - set(actual_results)
                correct_count = len(set(predicted_4) & actual_missing)

                # Cập nhật thống kê
                total_invested += bet_amount
                max_level_reached = max(max_level_reached, martingale_rec['level'])

                if correct_count == 4:
                    # THẮNG
                    win_amount = 32000
                    profit = win_amount - bet_amount
                    total_won += win_amount
                    win_count += 1

                    predictor.update_martingale_after_result(True, bet_amount)

                    print(f"🎯 Kì {period:3d} ({actual_time}) | "
                          f"Bet: {bet_amount:,}k | "
                          f"Prediction: {predicted_4} | "
                          f"Result: 4/4 ✅ | "
                          f"Profit: +{profit:,}k | "
                          f"Level: {martingale_rec['level']} → 1")
                else:
                    # THUA
                    loss_count += 1

                    predictor.update_martingale_after_result(False, bet_amount)

                    print(f"💸 Kì {period:3d} ({actual_time}) | "
                          f"Bet: {bet_amount:,}k | "
                          f"Prediction: {predicted_4} | "
                          f"Result: {correct_count}/4 ❌ | "
                          f"Loss: -{bet_amount:,}k | "
                          f"Level: {martingale_rec['level']} → {predictor.current_martingale_level}")

        except Exception as e:
            print(f"❌ Error kì {period}: {str(e)[:50]}")
            continue

    # Tổng kết
    net_profit = total_won - total_invested
    win_rate = (win_count / (win_count + loss_count)) * 100 if (win_count + loss_count) > 0 else 0

    print(f"\n" + "="*80)
    print("📊 MARTINGALE STRATEGY RESULTS")
    print("="*80)
    print(f"🎯 Total bets: {win_count + loss_count}")
    print(f"🏆 Wins: {win_count} ({win_rate:.1f}%)")
    print(f"💸 Losses: {loss_count}")
    print(f"📈 Max level reached: {max_level_reached}")
    print(f"💰 Total invested: {total_invested:,}k")
    print(f"💵 Total won: {total_won:,}k")
    print(f"📊 Net profit: {net_profit:,}k")
    print(f"📈 ROI: {(net_profit/total_invested)*100:.2f}%" if total_invested > 0 else "N/A")
    print(f"🎲 Final session profit: {predictor.session_profit:,}k")

    if net_profit > 0:
        print(f"✅ PROFITABLE! Martingale strategy worked!")
    else:
        print(f"❌ LOSS! Need to adjust strategy.")

    print("="*80)

    return {
        'total_bets': win_count + loss_count,
        'wins': win_count,
        'losses': loss_count,
        'win_rate': win_rate,
        'total_invested': total_invested,
        'total_won': total_won,
        'net_profit': net_profit,
        'max_level': max_level_reached,
        'session_profit': predictor.session_profit
    }

if __name__ == "__main__":
    main()
